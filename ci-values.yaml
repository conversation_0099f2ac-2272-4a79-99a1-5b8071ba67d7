#################### 默认配置，baseImageType必须填写，其它配置不填时，会生成默认值 #####################
deployment:
  baseImageType: "nginx"  # 必填，值可为["java", "node", "nginx", "caddy"]，默认值： "java"
  baseImage: "" # 非特殊情况无需填写
  deployZone: ""  # 值可为["edge",""],可不填
  routeMode: "history"  # vue/react项目可填["history","hash"],默认哈希
  overrideNamespace: ""  # 部署namespace，没有不标准情况无需填写
  overrideDeploymentName: ""  # 部署名称，默认为子系统简称，没有不标准情况无需填写
  overrideSystemNickname: ""  # 系统名称，默认为系统简称，没有不标准情况无需填写
  replicaCount: 1  # 部署副本数
  traceType: pinpoint # 值可为["opentelemetry","pinpoint"]
  corsPolicy: true # 是否跨域，值为true支持跨域，值为false不支持跨域
  enableCdn: true # 如果为true，生产环境执行ci时会重新编译
  envs: { } # 容器内环境变量 (可自己定义kv，形如: javaOpts: -Xmx2g -Xms2g )
  livenessProbe: # 健康检查，默认开启
    enabled: true
    failureThreshold: 5  #探针的最大失败次数，如果达到了最大的失败次数，容器将重新启动
    initialDelaySeconds: 60  #启动活存活探测之前容器启动的秒数。
    periodSeconds: 60  #执行探测的频率
    successThreshold: 1  #失败后探测成功的最小连续成功次数
    timeoutSeconds: 10  #探测超时的秒数
  readinessProbe: # 健康检查，默认开启
    enabled: true
    failureThreshold: 5  #探针的最大失败次数，如果达到了最大的失败次数，容器将重新启动
    initialDelaySeconds: 60  #启动活存活探测之前容器启动的秒数。
    periodSeconds: 60  #执行探测的频率
    successThreshold: 1  #失败后探测成功的最小连续成功次数
    timeoutSeconds: 10  #探测超时的秒数
  resources: # 资源配置
    limits:
      cpu: 2
      memory: 2Gi

service: # k8s服务配置
  sessionAffinity: "None"
  type: "ClusterIP"
  tcpPorts: { }

istio: # istio配置
  mesh:
    enabled: true
  virtualservice:
    enabled: true

configCenter: # 配置中心配置
  enabled: true
  type: "apollo"

#################### xhm-sit环境配置； 部署sit环境时，该配置下的值将覆盖上面的默认配置中的值 #####################
xhm-sit:
  deployment:
    hostAliases: [ ]  #写 入 pod 的 host 映射,列表类型的ip hostname结构，形如：- 127.0.0.1 foo.local
    replicaCount: 1
    resources:
      limits:
        cpu: 2
        memory: 2Gi

#################### xhm-uat环境配置： 部署uat时，该配置下的值将覆盖上面的默认配置中的值 #####################
xhm-uat:
  deployment:
    hostAliases: [ ]
    replicaCount: 1
    resources:
      limits:
        cpu: 2
        memory: 2Gi

#################### xhm-pet环境配置 部署pet时，该配置下的值将覆盖上面的默认配置中的值 #####################
xhm-pet:
  deployment:
    hostAliases: [ ]
    replicaCount: 1
    resources:
      limits:
        cpu: 2
        memory: 2Gi

#################### xhm-prod环境配置 部署西红门生产时，该配置下的值将覆盖上面的默认配置中的值 #####################
xhm-prod:
  deployment:
    hostAliases: [ ]
    replicaCount: 2
    resources:
      limits:
        cpu: 2
        memory: 2Gi

#################### gg-prod环境配置 部署硅谷生产时，该配置下的值将覆盖上面的默认配置中的值 #####################
gg-prod:
  deployment:
    hostAliases: [ ]
    replicaCount: 2
    resources:
      limits:
        cpu: 2
        memory: 2Gi
