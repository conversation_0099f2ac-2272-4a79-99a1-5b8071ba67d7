import { fileURLToPath, URL } from 'node:url'
import {defineConfig, loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'
import legacy from '@vitejs/plugin-legacy'
import vueDevTools from 'vite-plugin-vue-devtools'
import { createHtmlPlugin } from 'vite-plugin-html'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from '@vant/auto-import-resolver'

import { createStyleImportPlugin, VantResolve } from 'vite-plugin-style-import';

const PROXY_SERVER = 'https://www.unicompayment.com'

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, __dirname); // 根据 mode 来判断当前是何种环境
  console.log('-----------------------------')
  console.log('构建模式:', command)
  console.log('当前环境:', mode)
  console.log('Node.js版本:', process.version)
  console.log('当前环境变量VITE_EPAYENV:', env.VITE_EPAYENV)
  console.log('-----------------------------')

  // 从环境变量文件中获取配置
  const ASSETS_PUBLIC_PATH = env.VITE_ASSETS_PUBLIC_PATH
  const BASE_URL = env.VITE_BASE_URL
  const EPAYENV = env.VITE_EPAYENV
  console.log( '当前环境ASSETS_PUBLIC_PATH:', ASSETS_PUBLIC_PATH)
  console.log( '当前环境BASE_URL:', BASE_URL)
  console.log( '当前环境EPAYENV:', EPAYENV)
  return {
    // 设置静态资源基础路径
    base: ASSETS_PUBLIC_PATH,
    plugins: [
      vue(),
      createStyleImportPlugin({
        resolves: [VantResolve()],
        libs: [
          {
            libraryName: "vant",
            esModule: true,
            resolveStyle: name => {
              if (name === "show-toast" || name === "show-loading-toast") {
                return `../es/toast/style`;
              } else if (name === "show-dialog" || name === "show-confirm-dialog") {
                return `../es/dialog/style`;
              } else if (name.indexOf('set-') < 0 && name.indexOf('close-') < 0) {
                return `../es/${name}/style`;
              }
            }
          }
        ]
      }),
      // vueDevTools(),
      AutoImport({
        resolvers: [VantResolver()],
      }),
      Components({
        resolvers: [VantResolver()],
      }),
      legacy({
        targets: ['defaults', 'ie >= 11', 'chrome 52'],
        additionalLegacyPolyfills: ['regenerator-runtime/runtime']
      }),
      createHtmlPlugin({
        inject: {
          data: {
            EPAYENV: EPAYENV,
            BASE_URL: BASE_URL,
            __BUILD_VERSION__: JSON.stringify(new Date().toISOString().slice(0, 10).replace(/-/g, ''))
          },
        },
      }),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@api': fileURLToPath(new URL('./src/api', import.meta.url)),
        '@components': fileURLToPath(new URL('./src/components', import.meta.url)),
        '@views': fileURLToPath(new URL('./src/views', import.meta.url)),
        '@utils': fileURLToPath(new URL('./src/utils', import.meta.url)),
        '@store': fileURLToPath(new URL('./src/store', import.meta.url)),
        '@hooks': fileURLToPath(new URL('./src/hooks', import.meta.url)),
        '@router': fileURLToPath(new URL('./src/router', import.meta.url))
      },
    },
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          additionalData: `@import "@/assets/css/design-system.less";`
        },
      },
      // CSS压缩配置
      devSourcemap: false, // 开发环境下是否生成sourcemap
      // CSS代码分割
      cssCodeSplit: true,
      // CSS压缩方式
      minify: 'lightningcss', // 使用更快的lightningcss进行压缩
    },
    build: {
      // 输出目录配置
      outDir: 'dist',
      assetsDir: 'static',
      // 禁用生产环境的source map
      sourcemap: false,
      rollupOptions: {
        output: {
          manualChunks: function(id) {
            if (typeof id === 'string' && id.indexOf('node_modules/commonkit/lib/msJSBridge') !== -1) {
              return 'vendor-msJSBridge';
            }
            if (typeof id === 'string' && id.indexOf('node_modules/lodash') !== -1) {
              return 'vendor-lodash';
            }
            if (typeof id === 'string' && id.indexOf('node_modules/video.js') !== -1) {
              return 'vendor-video';
            }
          },
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: (assetInfo) => {
            var name = assetInfo && typeof assetInfo.name === 'string' ? assetInfo.name : '';
            if (/\.(png|jpg|jpeg|gif|svg)$/i.test(name)) {
              return 'static/img/[name]-[hash].[ext]';
            }
            return 'static/[ext]/[name]-[hash].[ext]';
          }
        }
      },
      // 图片压缩配置
      assetsInlineLimit: 10 * 1024, // 10kb以下的图片会被转为base64
      // 分块配置
      chunkSizeWarningLimit: 1000, // 1000kb
      minify: 'terser',
      terserOptions: {
        output: {
          // 压缩输出
          comments: false, // 去掉注释
        },
      },
    },
    server: {
      port: 8080, // 设置默认端口
      headers: {
        'Access-Control-Allow-Origin': '*'
      },
      proxy: {
        '/merchant-access-web': {
          target: PROXY_SERVER,
          changeOrigin: true
        },
        '/ps-ccms-core-front': {
          target: PROXY_SERVER,
          changeOrigin: true
        },
        '/ci-mcss-party-front': {
          target: PROXY_SERVER,
          changeOrigin: true
        },
        '/itf/bscorefront/api': {
          target: 'https://www.unicompayment.com/',
          changeOrigin: true,
          secure: false
        }
      },
    },
  }
})
