<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="preconnect" href="//img30.360buyimg.com">
    <link rel="preconnect" href="//img13.360buyimg.com">
    <link rel="dns-prefetch" href="//h5.cdn.unicompayment.com">
    <link rel="dns-prefetch" href="//media.cdn.unicompayment.com">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0,viewport-fit=cover">
  <title>Service Worker</title>
</head>

<body>
  <div id="sw-container"></div>
  <script>
    try {
      const urlParams = new URLSearchParams(window.location.search)
      const swValue = urlParams.get('sw')

      // 根据当前URL路径确定 Service Worker 路径前缀
      let swPathPrefix = '/ps-ccms-biz'
      const currentPath = window.location.pathname
      if (currentPath.includes('/canary-ps-ccms-biz')) {
        swPathPrefix = '/canary-ps-ccms-biz'
      }

      if (swValue === 'on') {
        if ('serviceWorker' in navigator && 'caches' in window) {
          window.addEventListener('load', function () {
            navigator.serviceWorker.register(`${swPathPrefix}/sw.js`).then(function (registration) {
              console.log('[Service Worker] 注册成功:', registration.scope)
              document.getElementById('sw-container').innerHTML += `<p>[Service Worker] 注册成功，${registration.scope}</p>`
            }).catch(function (err) {
              console.log('[Service Worker] 注册失败:', err)
              document.getElementById('sw-container').innerHTML += '<p>[Service Worker] 注册失败</p>'
            })
          })
        }
      } else {
        if ('serviceWorker' in navigator) {
          navigator.serviceWorker.getRegistration().then(function (registration) {
            if (registration) {
              console.log('[Service Worker] 正在注销...')
              registration.unregister().then(function (success) {
                if (success) {
                  console.log('[Service Worker] 注销成功')
                  document.getElementById('sw-container').innerHTML += '<p>[Service Worker] 注销成功</p>'
                } else {
                  console.log('[Service Worker] 注销失败')
                  document.getElementById('sw-container').innerHTML += '<p>[Service Worker] 注销失败</p>'
                }
              })
            } else {
              console.log('[Service Worker] 不存在')
              document.getElementById('sw-container').innerHTML += '<p>[Service Worker] 不存在</p>'
            }
          })
        }
        if ('caches' in window) {
          caches.keys().then(function (cacheNames) {
            if (cacheNames.length === 0) {
              console.log('[Service Worker] 没有缓存')
              document.getElementById('sw-container').innerHTML += '<p>[Service Worker] 没有缓存</p>'
            }
            cacheNames.forEach(function (cache) {
              console.log('[Service Worker] 删除缓存:', cache)
              caches.delete(cache)
              document.getElementById('sw-container').innerHTML += `<p>[Service Worker] 删除缓存完成，${cache}</p>`
            })
          })
        }
      }
    } catch (e) {
      document.getElementById('sw-container').innerHTML += '<p>操作失败</p>' + `<pre>${e.toString()}</pre>`
    }
  </script>
</body>

</html>
