export const afterSalesRoutes = [
  {
    path: '/wo-after-sales-entry',
    name: 'wo-after-sales-entry',
    meta: { login: true },
    component: () => import('@views/WoMall/OrderAfterSales/OrderAfterSalesEntry.vue')
  },
  {
    path: '/wo-after-sales-detail',
    name: 'wo-after-sales-detail',
    meta: { login: true },
    component: () => import('@views/WoMall/OrderAfterSales/OrderAfterSalesDetail.vue')
  },
  {
    path: '/wo-after-sales-refund',
    name: 'wo-after-sales-refund',
    meta: { login: true },
    component: () => import('@views/WoMall/OrderAfterSales/OrderAfterSalesRefund.vue')
  },
  {
    path: '/wo-after-sales-return',
    name: 'wo-after-sales-return',
    meta: { login: true },
    component: () => import('@views/WoMall/OrderAfterSales/OrderAfterSalesReturnGood.vue')
  },
  {
    path: '/wo-after-sales-express',
    name: 'wo-after-sales-express',
    meta: { login: true },
    component: () => import('@views/WoMall/OrderAfterSales/AfterSalesExpress.vue')
  },
  {
    path: '/wo-after-sales-logistics-info',
    name: 'wo-after-sales-logistics-info',
    meta: { login: true },
    component: () => import('@views/WoMall/OrderAfterSales/LogisticsInformation.vue')
  }
]
