export const orderRoutes = [
  {
    path: '/user/order/detail',
    name: 'user-order-detail',
    meta: { login: true },
    component: () => import('@views/WoMall/Order/OrderDetail/OrderDetail.vue'),
  },
  {
    path: '/user/order/search',
    name: 'user-order-search',
    meta: { login: true },
    component: () => import('@views/WoMall/Order/OrderSearch/IndexView.vue'),
  },
  {
    path: '/user/order/searchList',
    name: 'user-order-search-list',
    meta: { login: true },
    component: () => import('@views/WoMall/Order/OrderSearch/OrderSearch.vue'),
  },
  {
    path: '/user/order/recycle',
    name: 'user-order-recycle',
    meta: { login: true },
    component: () => import('@views/WoMall/Order/OrderRecycleBin/OrderRecycleBin.vue'),
  },
  {
    path: '/user/order/entry-express',
    name: 'user-order-entry-express',
    meta: { login: true },
    component: () => import('@views/WoMall/Order/OrderExpress/OrderEntryExpress.vue'),
  },
  {
    path: '/user/order/express',
    name: 'user-order-express',
    meta: { login: true },
    component: () => import('@views/WoMall/Order/OrderExpress/OrderExpress.vue'),
  },
  {
    path: '/user/order/express/muti',
    name: 'user-order-muti-express',
    meta: { login: true },
    component: () => import('@views/WoMall/Order/OrderExpress/OrderMutiExpress.vue'),
  },
  {
    path: '/orderconfirm',
    name: 'order-confirm',
    meta: { login: true },
    component: () => import('@views/WoMall/Order/OrderConfirm/OrderConfirm.vue'),
  },
  {
    path: '/user/order/list',
    name: 'user-order-list',
    meta: { login: true },
    component: () => import('@views/WoMall/Order/OrderList/OrderList.vue'),
  },
  {
    path: '/user/order/callback',
    name: 'user-order-callback',
    component: () => import('@views/WoMall/Order/OrderCallback/OrderCallback.vue'),
  }
]
