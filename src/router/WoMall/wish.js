export const wishRoutes = [
  {
    path: '/user/wish',
    name: 'user-wish',
    meta: {
      title: '我的心愿商品',
      login: true,
    },
    component: () => import('@views/WoMall/Wish/IndexView.vue')
  },
  {
    path: '/user/wish/list',
    name: 'user-wish-list',
    meta: {
      title: '我的心愿',
      login: true
    },
    component: () => import('@views/WoMall/Wish/WishListDetail.vue')
  },
  {
    path: '/user/wish/category/:categoryId?',
    name: 'user-wish-category',
    meta: {
      title: '心愿单分类',
      login: true,
    },
    component: () => import('@views/WoMall/Wish/WishCategory.vue')
  },
]
