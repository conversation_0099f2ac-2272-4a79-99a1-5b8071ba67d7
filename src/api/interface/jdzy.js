import {formGet, formPost, jsonPost} from "@api/config/index.js";
// 京东banner接口
export const getBannerList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/shelf/banner/queryBannerList', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取banner失败' }, null])
  })
})

// 京东引导图开关接口
export const getGuideFlag = () => new Promise(resolve => {
  formGet('/ps-ccms-core-front/v1/activityBit/getGuideFlag').then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '京东引导图开关接口失败' }, null])
  })
})

// 京东展示开关控制
export const getJdStatus = () => new Promise(resolve => {
  formGet('/ps-ccms-core-front/v1/activityBit/getNoticeFlag').then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '京东展示开关控制失败' }, null])
  })
})
// 分页搜索京东商品列表接口
export const getJdGoodsListByPage = (data) => new Promise(resolve => {
  jsonPost('/ps-ccms-core-front/v1/goods/getJdGoodsListByPage', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '分页搜索京东商品列表失败' }, null])
  })
})

// 商品地址转链接口
export const getJdClickUrl = (data) => new Promise(resolve => {
  jsonPost('/ps-ccms-core-front/v1/goods/getJdClickUrl', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '分页搜索京东商品列表失败' }, null])
  })
})
// 京东活动位接口

export const getActivityBitList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/shelf/banner/queryBannerList', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '京东活动位接口失败' }, null])
  })
})

// 京东宫格/热销/瀑布流接口（已弃用）
export const getCategoryList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/category/list', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '京东宫格/热销/瀑布流接口失败' }, null])
  })
})

// 京东宫格/热销/瀑布流获取分区id接口
export const getPartionList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/partion/list', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '京东宫格/热销/瀑布流获取分区id接口失败' }, null])
  })
})
// 京东商品列表展示接口

export const getPageList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/pagelist/simplified', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '京东商品列表展示接口失败' }, null])
  })
})

// 首页公告栏接口
export const queryNoticeList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/shelf/notice/queryNoticeList', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '首页公告栏接口失败' }, null])
  })
})
