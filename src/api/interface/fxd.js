import {formPost} from "@api/config/index.js";
// 查询能人收益信息
export const queryIncomeInfo = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/developer/income/detail', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '查询能人收益信息失败' }, null])
  })
})

// 查询能人订单列表
export const queryOrderList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/developer/order/detail', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '查询能人订单列表失败' }, null])
  })
})

// 检查连接中签名的有效性，是否被篡改
export const checkSignature = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/developer/sign/check', data).then(result => {
    if (result.returnCodeEnum === 'ILLEGAL_ARGUMENT_EXCEPTION') {
      // 链接已失效
      shared.app.$toast('链接已失效')
    } else if (result.code !== '0000') {
      shared.app.$toast(result.msg)
    }
    return [null, result]
  }).catch(() => {
    resolve([{ code: -1, msg: '查询能人订单列表失败' }, null])
  })
})
