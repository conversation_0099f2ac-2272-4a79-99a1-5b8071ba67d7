import {formPost} from "@api/config/index.js";

/**
 * 提交心愿单信息
 * @param data
 * @returns {Promise<unknown>}
 */
export const submitWishInfo = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/user/submitWish', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '提交心愿单信息失败' }, null])
  })
})

/**
 * 获取心愿信息列表
 * @param data
 * @returns {Promise<unknown>}
 */
export const getWishList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/user/wishlist', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取心愿信息列表失败' }, null])
  })
})
