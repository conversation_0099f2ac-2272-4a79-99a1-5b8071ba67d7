import { formPost } from '@api/config/index.js'
import {
  PS_CCMS_BRAND_LIST_DATA,
  PS_CCMS_CATEGORY_LIST_DATA
} from '@utils/types.js'
// 获取是否为白名单用户
export const isWhiteUser = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/isWhiteUser', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取数据失败' }, null])
  })
})

// 限制地区访问接口
export const getLimitAreaList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/limitArea/list', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取数据失败' }, null])
  })
})

// 获取电子券
export const getActiveList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/getElectronic', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取数据失败' }, null])
  })
})

// 用户白名单接口
export const isWhiteUserLimitCheck = goodsId => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/whilteUser/limitCheck', {
    goodsId: goodsId
  }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取数据失败' }, null])
  })
})

// 获取商品详情
export const getGoodsDetail = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/pagedetail', data).then(res => {
    resolve([res, res.data])
  }).catch(() => {
    resolve([{ code: -1, msg: '获取商品详情失败' }, null])
  })
})

// 获取商品列表
export const getGoodsList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/pagelist/simplified', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取商品列表失败' }, null])
  })
})

// 新版分类商品列表
export const skuPageList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/category/skuPageList', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取商品列表失败' }, null])
  })
})

// 分类页获取品牌
export const skuPageListBrandList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/skuPageList/brandList', {
    ...data
  },{}, {
    cacheFor: {
      // 缓存模式：内存+本地存储
      mode: 'memory-storage',
      // 缓存有效期：10分钟（单位毫秒）
      expire: 10 * 60 * 1000,
      // 缓存标签，用于后 续可能的缓存管理
      tag: PS_CCMS_BRAND_LIST_DATA,
    }
  }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取品牌信息失败' }, null])
  })
})

// 新分区接口  type=1，爆款手机；type=2，tap标签
export const getPartionList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/partion/list', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取分区失败' }, null])
  })
})

// 获取分类种类
export const getClassification = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/category/list', data, {} , {
    // 添加缓存配置
    cacheFor: {
      // 缓存模式：内存+本地存储
      mode: 'memory-storage',
      // 缓存有效期：10分钟（单位毫秒）
      expire: 10 * 60 * 1000,
      // 缓存标签，用于后续可能的缓存管理
      tag: PS_CCMS_CATEGORY_LIST_DATA,
    },
    // 单独设置重试配置，覆盖全局配置
    retry: {
      maxRetry: 3,      // 最大重试次数（首次请求+最多重试3次）
      retryDelay: 1000  // 重试延迟时间（单位：毫秒）
    }
  }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取分类种类失败' }, null])
  })
})

// 获取赠品详情
export const getGiftDetails = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/giftDetail', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取赠品详情失败' }, null])
  })
})

// 查询物流配送时间
export const queryPredictSkuPromise = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/queryPredictSkuPromise', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取物流配送时间失败' }, null])
  })
})

// 验证商品可售性
export const checkSkuSale = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/checkSkuSale', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '验证商品可售性失败' }, null])
  })
})

// 一键加入购物车
export const addOneClick = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/cart/addOneClick', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '一键加入购物车失败' }, null])
  })
})

// 获取商品详情
export const productIntroduction = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/goods/introduction', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取商品详情失败' }, null])
  })
})


export const setFrontCache = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/setFrontCache', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: ' 设置缓存数据' }, null])
  })
})

export const getFrontCache = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/getFrontCache', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: ' 获取缓存数据' }, null])
  })
})
