import {formPost, jsonPost} from "@api/config/index.js";
// 获取首页商品热销

export const getHotGoods = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/activityConf/list', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: ' 获取首页商品热销失败' }, null])
  })
})

// 获取首页banner图
export const getBannerList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/shelf/banner/queryBannerList', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: ' 获取首页banner图失败' }, null])
  })
})

// 获取已推荐供应商
export const getSupplierList = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/user/recommendSupplier/get', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取已推荐供应商失败' }, null])
  })
})

// 新增推荐供应商
export const addSupplier = data => new Promise(resolve => {
  jsonPost('/ps-ccms-core-front/v1/user/recommendSupplier/add', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '新增推荐供应商失败' }, null])
  })
})
