import {formPost} from "@api/config/index.js";

export const uploadVoucherImageUrl = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/uploadVoucherImageUrl', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '上传图片失败' }, null])
  })
})

export const userUploadExpressInfoForReturn = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/userUploadExpressInfoForReturn', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '上传物流失败' }, null])
  })
})

// 获取售后重新获取 退款类型按钮
export const reApplyButton = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/reApplyButton', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取退款类型按钮失败' }, null])
  })
})

// 新版售后接口，申请退款
export const applyOrderCancel = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/applyOrderCancel', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '申请退款失败' }, null])
  })
})

// 新版售后接口，申请售后入口可申请类型判断
export const getAfsSupportedType = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/getAfsSupportedType', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '申请售后类型判断失败' }, null])
  })
})

// 新版售后接口，jd申请退款、申请售后弹窗话术
export const jdPrompt = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/jd/prompt', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取数据失败JD' }, null])
  })
})

export const applyAfterSaleForRefund = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/applyAfterSaleForRefund', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '申请退款失败' }, null])
  })
})

export const applyAfterSaleForReturn = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/afterSale/applyAfterSaleForReturn', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '申请退货退款失败' }, null])
  })
})
