// 查询用户地区（省、市、县、镇）
import { formGet, formPost } from '@api/config/index.js'
import { PS_CCMS_AREA_LIST_DATA } from '@utils/types.js'

export const userAreaList = data => formGet({
  url: '/ps-ccms-core-front/v1/area/list',
  data
})

// 查询用户默认地址
export const queryUserDefaultAddr = () => new Promise(resolve => {
  // !shared.isEntryQueryCart && shared.app.$toast.queueLoading()
  formPost('/ps-ccms-core-front/v1/user/address/default').then(res => {
    // !shared.isEntryQueryCart && shared.app.$toast.queueClear()
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    // !shared.isEntryQueryCart && shared.app.$toast.queueClear()
    resolve([{ code: -1, msg: '获取用户默认地址失败' }, null])
  })
})

// 获取用户地址列表
export const queryUserAddrList = () => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/user/address/show').then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取用户地址失败' }, null])
  })
})

// 更新用户默认地址
export const updateUserDefaultAddr = (newAddressId) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/user/address/default/update', { newAddressId }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '更新用户地址失败' }, null])
  })
})

// 查询地区信息
export const queryAddrArea = (area) => new Promise(resolve => {
  formGet('/ps-ccms-core-front/v1/area/list', { area }, {}, {
    // 添加缓存配置
    cacheFor: {
      // 缓存模式：内存+本地存储
      mode: 'memory-storage',
      // 缓存有效期：10分钟（单位毫秒）
      expire: 10 * 60 * 1000,
      // 缓存标签，用于后续可能的缓存管理
      tag: PS_CCMS_AREA_LIST_DATA,
    }
  }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '查询地区信息失败' }, null])
  })
})

// 新增地址
export const addAddr = (data) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/user/address/add', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '新增用户地址失败' }, null])
  })
})

// 更新地址
export const editAddr = (data) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/user/address/update', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '更新用户地址失败' }, null])
  })
})

// 删除地址
export const deleteAddr = (addressId) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/user/address/delete', { addressId }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '删除用户地址失败' }, null])
  })
})
