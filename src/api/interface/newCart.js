import {formPost} from "@api/config/index.js";
// 获取购物车商品
export const query = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/cart/view', data).then(res => {
    // 后端说100% 返回数据
    resolve([null, res.data])
  }).catch(() => {
    resolve([{ code: -1, msg: '获取购物车商品失败' }, null])
  })
})

// 添加商品
export const add = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/cart/add', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '添加商品失败' }, null])
  })
})

// 更新商品（数量）
export const update = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/cart/update', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '更新商品（数量）失败' }, null])
  })
})

// 商品勾选
export const select = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/cart/select', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '商品勾选失败' }, null])
  })
})

// 删除商品
export const remove = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/cart/delGoods', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '删除商品失败' }, null])
  })
})

// 删除失效商品
export const removeInvalidGoods = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/cart/delInvalidGoods', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '删除商品失败' }, null])
  })
})

// 结算页购物车地址切换下单检查，立即购买下单检查
export const checkBuyNowOrderSku = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/cart/buyCheck', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '立即购买下单检查失败' }, null])
  })
})

// 失效-找相似
export const similarity = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/cart/find/similarity', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取数据失败' }, null])
  })
})

// 购物车下单检查
export const checkOrderSku = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/cart/verifyCart', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取数据失败' }, null])
  })
})

// 去结算=>下单商品列表
export const getSelectedGoods = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/cart/getSelectedGoods', data).then(res => {
    resolve([res, res.data])
  }).catch(() => {
    resolve([{ code: -1, msg: '获取下单商品列表失败' }, null])
  })
})

// 立即购买=>下单商品列表
export const getBuyNowGoods = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/cart/getBuyNowGoods', data).then(res => {
    resolve([res, res.data])
  }).catch(() => {
    resolve([{ code: -1, msg: '获取下单商品列表失败' }, null])
  })
})

// 获取快捷购物车
export const baseView = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/cart/baseView', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取快捷购物车数据失败' }, null])
  })
})
