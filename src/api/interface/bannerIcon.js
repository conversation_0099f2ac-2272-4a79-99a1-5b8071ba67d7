import {formPost} from "@api/config/index.js";
// 获取首页icon配置信息
export const getIconInfo = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/shelf/icon/queryIconList', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取首页icon配置信息失败' }, null])
  })
})
// 获取首页浮标配置信息
export const getFloatTag = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/floatTag/query', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取获取首页浮标配置信息失败' }, null])
  })
})
// 获取首页浮标配置信息

export const getOpenScreen = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/openScreen/get', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取获取首页浮标配置信息失败' }, null])
  })
})
// 获取首页banner配置信息
export const getBannerInfo = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/shelf/banner/queryBannerList', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取首页banner配置失败' }, null])
  })
})
