
import { getBizCode } from '@utils/curEnv.js'
import {formPost, jsonPost} from "@api/config/index.js";
import {cached} from "commonkit";

// 获取搜索历史信息
// 参数：bizCode
// 说明：bizCode是必填
export const getHistoryRecords = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/user/search/getHistoryRecords', {
    bizCode: getBizCode('QUERY')
  }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取搜索历史信息失败' }, null])
  })
})

// 获取订单搜索历史信息
// 参数：bizCode
// 说明：bizCode是必填
export const getOrderSearchHistory = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front//v2/order/getOrderSearchHistory', {
    bizCode: getBizCode('QUERY')
  }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取订单搜索历史信息失败' }, null])
  })
})

// 删除历史搜索信息
// 参数：bizCode,type,content
// 说明：bizCode是必填，type必填，ALL:值为ALL，删除全部；值为SINGLE，删除单个，content(要删除的内容，type=ALL,可以为空)
export const delHistoryRecord = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/user/search/delHistoryRecord', {
    bizCode: getBizCode('QUERY'),
    ...data
  }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '删除历史搜索信息失败' }, null])
  })
})
// 搜索指定内容
// 参数：bizCode,pageNumber,pageSize,keyword
// 说明：bizCode是必填，pageNumber当前页码，pageSize每页数量，keyword输入的关键字
export const searchKeyWord = data => new Promise(resolve => {
  jsonPost(`/ps-ccms-core-front/v1/user/search/search?testDMX=${data.testDMX}`, {
    bizCode: getBizCode('QUERY'),
    ...data
  }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '搜索信息失败' }, null])
  })
})

export const getSearchBrandNames = data => new Promise(resolve => {
  jsonPost(`/ps-ccms-core-front/v1/user/search/brandNames?testDMX=${data.testDMX}`, {
    bizCode: getBizCode('QUERY'),
    ...data
  }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取品牌信息失败' }, null])
  })
})
export const getSearchBrandNamesCached = cached(getSearchBrandNames)
