import { urlAppend, useLogin } from 'commonkit';
import shared from '@/shared.js';
import { getBizCode } from '@utils/curEnv.js';
import { curChannelBiz, loginType } from '@utils/storage.js'

/**
 * 成功响应处理器
 */
export const onSuccess = (response) => {
  const json = response.data;
  // 在调用购物车接口下，不论接口是否成功，均关闭 isEntryQueryCart 状态
  if (response.config.url === '/ps-ccms-core-front/v2/cart/view') {
    shared.isEntryQueryCart = false;
  }

  // 处理未登录情况
  if (json.code !== '0000' && (json.returnCodeEnum === 'USER_NOT_LOGIN' || json.code === '0100')) {
    // 如果当前是入口查询购物车状态，并且接口是地址/购物车地址，返回未登录，不进行强行登录处理
    if (shared.isEntryQueryCart &&
      (response.config.url === '/ps-ccms-core-front/v1/user/Address/default' ||
        response.config.url === '/ps-ccms-core-front/v2/cart/view')) {
      return json;
    }

    const [state, , toLogin] = useLogin();
    const url = window.location.href;
    const callbackUrl = urlAppend(url, {
      distri_biz_code: getBizCode(),
      biz_channel_code: curChannelBiz.get()
    });
    toLogin(
      { callbackUrl },
      { useHistoryReplace: true, loginType: loginType.get() || '0' }
    );
  }
  return json;
};

/**
 * 错误响应处理器
 */
export const onError = (error) => {
  console.error('请求失败:', error);
  
  // 对于可重试的错误，直接抛出让alova的重试机制处理
  // 网络错误、超时错误、5xx服务器错误等
  if (!error.response || 
      error.response.status >= 500 || 
      error.response.status === 408 || 
      error.response.status === 429) {
    // 直接抛出错误，让alova的重试机制处理
    throw error;
  }
  
  // 其他错误（4xx客户端错误等）不重试，直接返回格式化的错误信息
  throw {
    code: error.response?.status || -1,
    msg: error.message || '网络请求失败',
    response: error.response,
    config: error.config,
    originalError: error
  };
};
