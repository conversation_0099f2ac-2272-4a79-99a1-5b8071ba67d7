import { createAlova } from 'alova';
import { axiosRequestAdapter } from '@alova/adapter-axios';
import VueHook from 'alova/vue';
import { onSuccess, onError } from './responseHandlers';

/**
 * 创建alova实例
 * 该实例用于统一管理应用的所有请求行为
 */
const alovaInstance = createAlova({
  // 指定使用的状态管理钩子，这里使用Vue的响应式系统
  statesHook: VueHook,
  // 全局请求超时设置（单位：毫秒）
  timeout: 30000,
  // 使用的请求适配器，这里配置为axios
  requestAdapter: axiosRequestAdapter(),
  // 响应拦截器配置
  responded: {
    onSuccess,
    onError
  },
  // 全局重试配置 - alova v3的正确配置方式
  retry: {
    retry: 3,         // 重试次数
    delay: 100,      // 重试延迟（毫秒）
    // 重试条件判断函数
    when: (error) => {
      // 网络错误或5xx服务器错误时重试
      if (!error.response) return true;
      const status = error.response.status;
      return status >= 500 || status === 408 || status === 429;
    }
  },
  localCache: null, // 禁用缓存以确保重试生效
});

// 导出alova实例，以便在需要时直接使用
export default alovaInstance;
