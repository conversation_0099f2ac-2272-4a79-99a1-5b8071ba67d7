import { assign } from "lodash-es";
import qs from "qs";

/**
 * 生成JSON格式POST请求配置
 */
export const getJsonPostConfig = (data = {}, headers = {}, config = {}) => ({
  body: {
    ...data,
    // _t: Date.now()
  },
  headers: assign({}, { 'Content-Type': 'application/json;charset=UTF-8' }, headers),
  ...config
});

/**
 * 生成表单GET请求配置
 */
export const getFormGetConfig = (data = {}, headers = {}, config = {}) => ({
  params: {
    ...data,
    // _t: Date.now()
  },
  headers: assign({}, headers),
  ...config
});

/**
 * 生成表单POST请求配置
 */
export const getFormPostConfig = (data = {}, headers = {}, config = {}) => {
  const postData = qs.stringify({
    ...data,
    // _t: Date.now()
  }, { indices: false });

  return {
    body: postData,
    headers: assign({}, { 'Content-Type': 'application/x-www-form-urlencoded' }, headers),
    ...config
  };
};
