// 按照 alova 规范重新封装 API 方法
import alovaInstance from "./alova.config.js";
import { getJsonPostConfig, getFormGetConfig, getFormPostConfig } from "./requestConfig.js";

/**
 * JSON格式POST请求
 */
export const jsonPost = (url, data = {}, headers = {}, config = {}) => {
  const requestConfig = getJsonPostConfig(data, headers, config);
  return alovaInstance.Post(url, requestConfig.body, {
    headers: requestConfig.headers,
    // 添加重试配置
    retry: config.retry !== false ? {
      retry: config.maxRetry || 3,      // 最大重试次数
      backoff: {
        delay: config.retryDelay || 1000, // 重试延迟
        multiplier: 1.5,                   // 延迟倍数
        maxDelay: 10000                    // 最大延迟
      }
    } : false,
    ...config
  });
};

/**
 * 表单GET请求
 */
export const formGet = (url, data = {}, headers = {}, config = {}) => {
  const requestConfig = getFormGetConfig(data, headers, config);
  return alovaInstance.Get(url, {
    params: requestConfig.params,
    headers: requestConfig.headers,
    // 添加重试配置
    retry: config.retry !== false ? {
      retry: config.maxRetry || 3,      // 最大重试次数
      backoff: {
        delay: config.retryDelay || 1000, // 重试延迟
        multiplier: 1.5,                   // 延迟倍数
        maxDelay: 10000                    // 最大延迟
      }
    } : false,
    ...config
  });
};

/**
 * 表单POST请求
 */
export const formPost = (url, data = {}, headers = {}, config = {}) => {
  const requestConfig = getFormPostConfig(data, headers, config);
  return alovaInstance.Post(url, requestConfig.body, {
    headers: requestConfig.headers,
    // 添加重试配置
    retry: config.retry !== false ? {
      retry: config.maxRetry || 3,      // 最大重试次数
      backoff: {
        delay: config.retryDelay || 1000, // 重试延迟
        multiplier: 1.5,                   // 延迟倍数
        maxDelay: 10000                    // 最大延迟
      }
    } : false,
    ...config
  });
};