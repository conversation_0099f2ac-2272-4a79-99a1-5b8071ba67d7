// 按模块分组导出，方便使用
import * as addressApi from './interface/address.js'
import * as afterSalesApi from './interface/afterSales.js'
import * as bannerIconApi from './interface/bannerIcon.js'
import * as digitalVillageApi from './interface/digitalVillage.js'
import * as flhApi from './interface/flh.js'
import * as fpApi from './interface/fp.js'
import * as fxdApi from './interface/fxd.js'
import * as goodsApi from './interface/goods.js'
import * as jdzyApi from './interface/jdzy.js'
import * as loanProductionApi from './interface/loanproduction.js'
import * as cartApi from './interface/newCart.js'
import * as orderApi from './interface/order.js'
import * as searchApi from './interface/search.js'
import * as wishApi from './interface/wish.js'
import * as zqApi from './interface/zq.js'

// 分组导出 - 推荐使用方式
export const address = addressApi
export const afterSales = afterSalesApi
export const bannerIcon = bannerIconApi
export const digitalVillage = digitalVillageApi
export const flh = flhApi
export const fp = fpApi
export const fxd = fxdApi
export const goods = goodsApi
export const jdzy = jdzyApi
export const loanProduction = loanProductionApi
export const cart = cartApi
export const order = orderApi
export const search = searchApi
export const wish = wishApi
export const zq = zqApi

// 兼容性导出 - 保持原有的单个函数导出方式
export * from './interface/address.js'
export * from './interface/afterSales.js'
export * from './interface/bannerIcon.js'
export * from './interface/digitalVillage.js'
export * from './interface/flh.js'
export * from './interface/fp.js'
export * from './interface/fxd.js'
export * from './interface/goods.js'
export * from './interface/jdzy.js'
export * from './interface/loanproduction.js'
export * from './interface/newCart.js'
export * from './interface/order.js'
export * from './interface/search.js'
export * from './interface/wish.js'
export * from './interface/zq.js'