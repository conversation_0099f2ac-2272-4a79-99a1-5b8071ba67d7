// 集中管理所有插件注册
import VueWechatTitle from 'vue-wechat-title'
import VideoJsPlayer from '@videojs-player/vue'
import 'video.js/dist/video-js.css'
import { Lazyload } from 'vant'

// 图片资源
import loadingImgPlaceHolder from '@/assets/images/placeholder-loading.png'
import errorImgPlaceHolder from '@/assets/images/placeholder-error.png'


// 全局配置
export function registerPlugins (app) {
  // 注册 vue-wechat-title
  app.use(VueWechatTitle)

  // 注册 VideoJsPlayer 组件
  app.component('VideoJsPlayer', VideoJsPlayer)

  // 注册 Vant Lazyload 插件，配置加载中和加载失败的占位图
  app.use(Lazyload, {
    loading: loadingImgPlaceHolder,
    error: errorImgPlaceHolder
  })
}
