import { loginRun, shareInit } from 'commonkit'
import { setEntryQuerystring } from '@/utils/entryQuerystring'
import { getBizCode } from '@/utils/curEnv'
import imgToastLoading from '@/assets/images/woLoading.gif'
import jdImgToastLoading from '@/assets/images/jdLoading.gif'
import { setToastDefaultOptions } from 'vant';

// 初始化全局配置
export function initializeApp () {
  // 处理 commonkit login 的默认方法
  window.COMMONKIT_ENV_APP_NAME = 'PS-CCMS-BIZ-WEB'
  window.COMMONKIT_LOGIN_PATH = '/ps-ccms-core-front/api'
  window.COMMONKIT_WOPAY_CLIENT_ID = 'a4c07010-f246-4ee9-9824-19c4dbcef6b3'

  // 处理相关参数
  setEntryQuerystring(window.location.search.substring(1))

  // 分享功能初始化
  shareInit()

  // 登录初始化
  loginRun({ autoQueryStatus: false })

  // 设置 Toast loading
  setToastDefaultOptions('loading', {
    duration: 0,
    icon: getBizCode() !== 'ygjd' ? imgToastLoading : jdImgToastLoading,
    forbidClick: true,
    loadingType: 'circular',
    className: 'wo-loading'
  })

  // 设置 Toast 默认选项
  setToastDefaultOptions({
    forbidClick: true,
    duration: 2000
  })
}

// 修复特定环境问题
export function fixEnvironmentIssues () {
  // 手厅测试客户端高度问题修复
  const fixStHeight = () => {
    const useragent = window.navigator.userAgent
    const isSt = /unicom{version/i.test(useragent)
    if (isSt && window.screen.height === document.body.offsetHeight) {
      document.body.classList.add('st-height-fix')
    }
  }
  fixStHeight()
}
