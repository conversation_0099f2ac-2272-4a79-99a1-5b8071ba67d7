<template>
  <WoCard>
    <div class="goods-section">
      <div class="goods-list">
        <GoodsListItem
          v-for="item in goodsList"
          :key="item.id"
          :item="item"
          :image-size="imageSize"
          :min-height="minHeight"
          :show-actions="showActions"
        >
          <template #actions="slotProps">
            <slot name="actions" :item="slotProps.item"></slot>
          </template>
        </GoodsListItem>
      </div>
    </div>
  </WoCard>
</template>

<script setup>
import WoCard from '@components/WoElementCom/WoCard.vue'
import GoodsListItem from '@/components/Common/GoodsListItem.vue'

const props = defineProps({
  // 商品列表数据
  goodsList: {
    type: Array,
    required: true,
    default: () => []
  },
  // 图片尺寸
  imageSize: {
    type: Number,
    default: 90
  },
  // 最小高度
  minHeight: {
    type: Number,
    default: 135
  },
  // 是否显示操作按钮
  showActions: {
    type: <PERSON>olean,
    default: false
  }
})
</script>

<style scoped lang="less">
.goods-section {
  width: 100%;
  .goods-list {
    width: 100%;
  }
}
</style>
