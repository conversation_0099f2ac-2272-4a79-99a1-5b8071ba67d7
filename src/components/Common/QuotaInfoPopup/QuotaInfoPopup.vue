<template>
  <div class="quota-info-popup">
    <van-popup class="quota-info-popup-content" v-model:show="show" round position="bottom" :style="{ height: '70%' }"
      @close="onClose">
      <div class="quota-info-popup-title">慰问活动</div>
      <div class="quota-info-popup-container">
        <div class="quota-info-popup-tabs">
          <van-tabs ref="popupTabs" v-model:active="active" @change="onChange">
            <van-tab title="可用活动">
              <div class="quota-info-content"
                v-if="availableActivityQuotaList && availableActivityQuotaList.length > 0">
                <div class="quota-info-tips">
                  <img class="tips-icon" src="./assets/tips.png" alt="tips">
                  <span class="tips-content">已为您展示可用优惠，可在支付时选择</span>
                </div>
                <div ref="quotaInfoDetail" class="quota-info-detail">
                  <template v-if="grantAmount <= 0">
                    <div class="quota-info-card" v-for="item in availableActivityQuotaList" :key="item.activityNo">
                      <div class="quota-money">￥{{ fenToYuan(item.balanceAmount) }}</div>
                      <div class="quota-info">
                        <h3 class="active-name">{{ item.activityName }}</h3>
                        <p class="active-validity-period">有效期：{{ item.startTime }} ~ {{ item.endTime }}</p>
                      </div>
                    </div>
                  </template>
                  <template v-if="grantAmount > 0">
                    <div class="quota-info-special">
                      <h3 class="active-name">{{ availableActivityQuota.activityName }}</h3>
                      <div class="quota-money-card">
                        <div class="quota-money-item">
                          <div class="quota-money-number">{{ fenToYuan(availableActivityQuota.grantAmount) }}</div>
                          <div class="quota-money-type">已发放积点</div>
                        </div>
                        <div class="quota-money-item">
                          <div class="quota-money-number">{{ fenToYuan(availableActivityQuota.usedAmount) }}</div>
                          <div class="quota-money-type">已使用积点</div>
                        </div>
                        <div class="quota-money-item">
                          <div class="quota-money-number">{{ fenToYuan(availableActivityQuota.amount) }}</div>
                          <div class="quota-money-type">剩余积点</div>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
              <van-empty v-else class="custom-image" :image="noDataImg" description="暂无活动" />
            </van-tab>
            <van-tab title="不可用活动">
              <div class="quota-info-content quota-info-content-no"
                v-if="unAvailableActivityQuotaList && unAvailableActivityQuotaList.length > 0">
                <div ref="quotaInfoDetail" class="quota-info-detail">
                  <div class="quota-info-card quota-info-card-no" v-for="item in unAvailableActivityQuotaList"
                    :key="item.activityNo">
                    <div class="quota-money">￥{{ fenToYuan(item.balanceAmount) }}</div>
                    <div class="quota-info">
                      <h3 class="active-name">{{ item.activityName }}</h3>
                      <p class="active-validity-period">有效期：{{ item.startTime }} ~ {{ item.endTime }}</p>
                      <p class="active-unavailable">不可用原因：活动不适用于该商城</p>
                    </div>
                  </div>
                </div>
              </div>
              <van-empty v-else class="custom-image" :image="noDataImg" description="暂无活动" />
            </van-tab>
          </van-tabs>
        </div>
      </div>
      <div class="quota-info-op">
        <WoButton type="gradient" block @click="onClose">确定</WoButton>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, computed, watch } from 'vue'
import { fenToYuan } from '@/utils/amount'
import { get, defaultTo } from 'lodash'
import noActive from './assets/no-active.png'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'

// Props
const props = defineProps({
  modelValue: { type: Boolean, default: false },
  availableActivityQuota: {
    type: Object,
    default: () => ({})
  },
  unAvailableActivityQuota: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// Refs
const popupTabs = ref(null)
const quotaInfoDetail = ref(null)

// Reactive data
const noDataImg = ref(noActive)
const show = ref(false)
const active = ref(0)

// Computed
const availableActivityQuotaList = computed(() => {
  const quotaInfo = get(props.availableActivityQuota, 'quotaInfo', [])
  return defaultTo(quotaInfo, [])
})

const unAvailableActivityQuotaList = computed(() => {
  const quotaInfo = get(props.unAvailableActivityQuota, 'quotaInfo', [])
  return defaultTo(quotaInfo, [])
})

const grantAmount = computed(() => {
  return props.availableActivityQuota?.grantAmount || 0
})

// Watch
watch(
  () => props.modelValue,
  (val) => {
    show.value = val
  },
  { immediate: true }
)

// Methods
const onClose = () => {
  show.value = false
  emit('update:modelValue', false)
}

const onChange = (name) => {
  active.value = name
  emit('change', name)
}
</script>

<style scoped lang="less">
.quota-info-popup :deep(.van-popup) {
  box-sizing: border-box;
  padding: 20px;
}

.quota-info-popup {
  height: 100%;

  .quota-info-popup-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    .quota-info-op {
      width: 100%;
      margin-top: 20px;
      flex-shrink: 0;
    }
  }

  .quota-info-popup-title {
    margin-bottom: 20px;
    font-size: 20px;
    color: #171E24;
    font-weight: 700;
    text-align: center;
    flex-shrink: 0;
  }

  .quota-info-popup-container {
    width: 100%;
    flex: 1;
    overflow: hidden;
    min-height: 0;

    .quota-info-popup-tabs {
      width: 100%;
      height: 100%;

      :deep(.van-tabs) {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      :deep(.van-tabs__wrap) {
        height: 44px;
        flex-shrink: 0;
      }

      :deep(.van-tabs__content) {
        flex: 1;
        overflow: hidden;
      }

      :deep(.van-tab__pane) {
        height: 100%;
        overflow: hidden;
      }

      :deep(.van-tabs__line) {
        width: 40px !important;
      }

      :deep(.van-tab--active .van-tab__text) {
        font-size: 16px;
        color: #171E24 !important;
        line-height: 1.5;
        font-weight: 700 !important;
      }

      :deep(.van-tab .van-tab__text) {
        font-size: 16px;
        line-height: 1.5;
        font-weight: 400;
      }

      :deep(.van-empty) {
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        padding: 40px 20px !important;
      }
    }

    .quota-info-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 16px 0 0;
      box-sizing: border-box;

      .quota-info-tips {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        margin-bottom: 16px;
        background: rgba(255, 75, 51, 0.05);
        border-radius: 8px;
        flex-shrink: 0;

        .tips-icon {
          margin-right: 8px;
          width: 20px;
          height: 20px;
          flex-shrink: 0;
        }

        .tips-content {
          font-size: 14px;
          color: #FF4B33;
          font-weight: 500;
          line-height: 1.4;
        }
      }

      .quota-info-detail {
        flex: 1;
        overflow-y: auto;
        padding-right: 4px;
        min-height: 0;

        &::-webkit-scrollbar {
          width: 4px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        .quota-info-card {
          box-sizing: border-box;
          display: flex;
          align-items: flex-start;
          padding: 10px;
          margin-bottom: 16px;
          background: #FFF2EB;
          border: 1px solid rgba(255, 205, 181, 1);
          border-radius: 12px;
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(255, 120, 10, 0.1);
          }

          &:last-child {
            margin-bottom: 0;
          }

          .quota-money {
            min-width: 100px;
            margin-right: 10px;
            font-size: 20px;
            color: #FF780A;
            line-height: 1.2;
            font-weight: 700;
            flex-shrink: 0;
          }

          .quota-info {
            flex: 1;
            min-width: 0;

            .active-name {
              margin-bottom: 12px;
              font-size: 16px;
              color: #171E24;
              line-height: 1.4;
              font-weight: 600;
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
              text-overflow: ellipsis;
              word-break: break-all;
            }

            .active-validity-period {
              font-size: 12px;
              color: #879099;
              font-weight: 400;
              line-height: 1.4;
              margin-bottom: 8px;
            }

            .active-unavailable {
              font-size: 14px;
              color: #171E24;
              line-height: 1.4;
              font-weight: 400;
            }
          }
        }

        .quota-info-special {
          box-sizing: border-box;
          padding: 24px 20px;
          margin-bottom: 16px;
          background: #FFF2EB;
          border: 1px solid rgba(255, 205, 181, 1);
          border-radius: 12px;
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(255, 120, 10, 0.1);
          }

          &:last-child {
            margin-bottom: 0;
          }

          .active-name {
            margin-bottom: 20px;
            font-size: 18px;
            color: #171E24;
            line-height: 1.4;
            font-weight: 600;
            text-align: center;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            word-break: break-all;
          }

          .quota-money-card {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 16px;

            .quota-money-item {
              flex: 1;
              text-align: center;

              .quota-money-number {
                font-size: 22px;
                color: #FF780A;
                font-weight: 700;
                margin-bottom: 8px;
                line-height: 1.2;
              }

              .quota-money-type {
                font-size: 12px;
                color: #879099;
                line-height: 1.2;
                font-weight: 400;
              }
            }
          }
        }

        .quota-info-card-no {
          background: #F8F9FA;
          border: 1px solid rgba(234, 234, 234, 1);

          .quota-money {
            color: #B1BEC9;
          }

          .quota-info {
            .active-name {
              color: #B1BEC9;
            }

            .active-validity-period {
              color: #B1BEC9;
            }
          }
        }
      }
    }

    .quota-info-content-no {
      padding-top: 16px;
    }
  }
}
</style>
