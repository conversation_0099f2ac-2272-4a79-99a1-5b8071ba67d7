<template>
  <span class="price-display" :class="[sizeClass, colorClass, { 'price-bold': bold }]">
    <span class="currency">¥</span>
    <span class="integer">{{ integerPart }}</span>
    <span class="decimal" v-if="decimalPart">.{{ decimalPart }}</span>
  </span>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  price: {
    type: [Number,String],
    required: false
  },
  size: {
    type: String,
    default: 'medium',
    validator: value => ['small', 'medium', 'large'].includes(value)
  },
  color: {
    type: String,
    default: 'primary',
    validator: value => ['primary', 'orange', 'red', 'white'].includes(value)
  },
  bold: {
    type: Boolean,
    default: true
  }
})

// 计算价格的整数和小数部分
const priceFormatted = computed(() => {
  if (props.price === null || props.price === undefined) {
    return '--.-'
  }
  const price = props.price / 100 // 假设传入的是分为单位
  return price.toFixed(2)
})

const integerPart = computed(() => {
  if (props.price === null || props.price === undefined) {
    return '--'
  }
  return priceFormatted.value.split('.')[0]
})

const decimalPart = computed(() => {
  if (props.price === null || props.price === undefined) {
    return '-'
  }
  return priceFormatted.value.split('.')[1]
})

const sizeClass = computed(() => `price-${props.size}`)
const colorClass = computed(() => `price-${props.color}`)
</script>

<style scoped lang="less">
.price-display {
  font-family: 'D-DIN-PRO SemiBold';
  display: inline-flex;
  align-items: baseline;

  .currency {
    margin-right: 2px;
    font-weight: @font-weight-400;
  }

  .integer {
    font-weight: @font-weight-400;
  }

  .decimal {
    font-weight: @font-weight-400;
  }

  // 尺寸变体
  &.price-small {
    .currency {
      font-size: @font-size-13;
    }

    .integer {
      font-size: @font-size-17;
    }

    .decimal {
      font-size: @font-size-13;
    }
  }

  &.price-medium {
    .currency {
      font-size: @font-size-16;
    }

    .integer {
      font-size: @font-size-20;
    }

    .decimal {
      font-size: @font-size-16;
    }
  }

  &.price-large {
    .currency {
      font-size: @font-size-18;
    }

    .integer {
      font-size: @font-size-22;
    }

    .decimal {
      font-size: @font-size-18;
    }
  }

  // 颜色变体
  &.price-primary {
    color: @text-color-primary;
  }

  &.price-orange {
    color: @color-orange;
  }

  &.price-red {
    color: @color-red;
  }

  &.price-white {
    color: @color-white;
  }

  // 加粗变体
  &.price-bold {
    .currency {
      font-weight: @font-weight-600;
    }

    .integer {
      font-weight: @font-weight-700;
    }

    .decimal {
      font-weight: @font-weight-700;
    }
  }
}
</style>
