<template>
  <div class="main-layout">
    <div class="main-layout__content" :class="contentClass">
      <slot />
    </div>
    <nav class="main-layout__nav" ref="navRef">
      <button v-for="(item, index) in tabs" :key="`tab-${index}`" class="nav-item" :class="getNavItemClass(item, index)"
        type="button" @click="handleTabClick(index)" :aria-label="item.title || '导航'"
        :aria-current="activeTab === index ? 'page' : undefined">
        <div class="nav-item__icon-wrapper">
          <img class="nav-item__icon" :class="getIconClass(item)" :src="getIconSrc(item, index)" :alt="item.title || ''"
            loading="eager" decoding="async">
        </div>
        <span v-if="item.title" class="nav-item__text" :class="getTextClass(index)">
          {{ item.title }}
        </span>
      </button>
    </nav>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
// import { useStore } from 'vuex'
import { getBizCode } from '@/utils/curEnv'
// import { gotoHome } from '@/utils/jump'
import { getTabConfig, getAllImages } from './tabConfigs'

// 接收props
const props = defineProps({
  scroll: {
    type: String,
    default: 'auto'
  }
})

// 获取路由和store实例
const router = useRouter()
const route = useRoute()
// const store = useStore()

// 定义refs
const navRef = ref(null)

// 预加载所有图片资源，解决闪烁问题
const preloadImages = () => {
  const allImages = getAllImages()
  allImages.forEach(src => {
    const img = new Image()
    img.src = src
  })
}

// 定义响应式数据
const tabs = ref([])

// 计算属性
const bizCode = computed(() => getBizCode() || '')

const activeTab = computed(() => {
  const path = route.path
  return tabs.value.findIndex(item => path.indexOf(item.path) === 0)
})

const contentClass = computed(() => {
  return `main-layout__content--${props.scroll}`
})

// 新增的计算方法
const getNavItemClass = (item, index) => {
  const classes = []
  if (item.className) classes.push('nav-item--promotion')
  if (activeTab.value === index) classes.push('nav-item--active')
  return classes
}

const getIconClass = (item) => {
  const classes = []
  if (!item.className) classes.push('nav-item__icon--normal')
  if (!item.title) classes.push('nav-item__icon--large')
  return classes
}

const getIconSrc = (item, index) => {
  return activeTab.value === index ? item.activeImg : item.img
}

const getTextClass = (index) => {
  const classes = []
  if (activeTab.value === index) {
    classes.push('nav-item__text--active')
    if (bizCode.value === 'ygjd') {
      classes.push('nav-item__text--jd')
    }
  }
  return classes
}

// 方法
const handleTabClick = (index) => {
  // 如果icon大于4个且点击的是第三个icon,则此icon为活动icon
  if (tabs.value.length > 4 && index === 2) {
    window.location.href = tabs.value[index].path
    return
  }
  if (activeTab.value === index) return

  const targetPath = tabs.value[index].path

  if (targetPath === '/home') {
    // 首页，特殊处理
    router.push({ path: targetPath })
  } else if (targetPath === '/category') {
    // 分类页，特殊处理，增加随机数
    router.push({ path: targetPath, query: { _t: Date.now().toString() } })
  } else {
    router.push({ path: targetPath })
  }
}

// 加载tab菜单中间的活动icon
const getPromotionIcon = async () => {
  if (getBizCode() !== 'ziying') return
  // await store.dispatch('navPromotion/query', { bizCode: getBizCode('QUERY') })
  // const menuItem = store.state.navPromotion.menuItem
  // if (menuItem.img) {
  //   // 把活动icon加载到第三个位置
  //   tabs.value.splice(2, 0, menuItem)
  // }
}

// 生命周期钩子
onMounted(() => {
  // 预加载图片资源，解决闪烁问题
  preloadImages()

  // 根据业务代码获取对应的tab配置
  const bizCode = getBizCode()
  tabs.value = getTabConfig(bizCode)

  getPromotionIcon()
})
</script>

<style lang="less" scoped>
// 删除这些变量声明，直接在使用处替换
// @nav-height: 49px;
// @nav-icon-size: 23px;
// @nav-icon-large-size: 41px;
// @nav-promotion-icon-width: 60px;
// @nav-promotion-icon-height: 50px;
// @nav-padding-vertical: 6px;
// @nav-padding-horizontal: 5px;
// @nav-text-margin-top: 2px;

// 安卓8以下单独处理
.android_8 {
  .main-layout {
    display: flex;
    height: 100vh;

    .main-layout__content {
      flex: 1;
      overflow: hidden;
      padding-bottom: 49px; // 直接使用数值

      &--none {
        height: calc(100vh - 49px); // 直接使用数值
      }
    }

    .main-layout__nav {
      height: 49px; // 直接使用数值
    }
  }
}

.main-layout {
  display: flex;
  height: 100vh;
  flex-direction: column;

  &__content {
    flex: 1;
    overflow: hidden;
    padding-bottom: 49px; // 直接使用数值

    &--none {
      height: calc(100vh - 49px - var(--saib)); // 直接使用数值
    }

    &--auto {
      height: auto;
    }
  }

  &__nav {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    display: flex;
    height: 49px; // 直接使用数值
    border-top: 1px solid @divider-color-base;
    background: @bg-color-white;
    // 添加硬件加速，提升性能
    transform: translateZ(0);
    will-change: transform;
  }
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 6px @padding-page; // 使用design-system变量
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
  // 优化点击体验
  -webkit-tap-highlight-color: transparent;
  transition: opacity 0.2s ease;

  &:active {
    opacity: @opacity-07;
  }

  &--promotion {
    padding: 0;
  }

  &__icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__icon {
    display: block;
    // 防止图片闪烁的关键样式
    image-rendering: -webkit-optimize-contrast;
    backface-visibility: hidden;
    transform: translateZ(0);

    &--normal {
      width: 23px; // 直接使用数值
      height: 23px; // 直接使用数值
    }

    &--large {
      width: 41px; // 直接使用数值
      height: 41px; // 直接使用数值
    }
  }

  &__text {
    margin: 2px 0 0; // 直接使用数值
    font-size: @font-size-12;
    color: @text-color-tertiary;
    line-height: 1;
    // 防止文字抖动
    font-feature-settings: 'tnum';

    &--active {
      color: @theme-color;
    }

    &--jd {
      color: @color-red !important;
    }
  }

  // 推广图标特殊样式
  &--promotion &__icon {
    width: 60px; // 直接使用数值
    height: 50px; // 直接使用数值
  }
}

// 性能优化：减少重绘重排
* {
  box-sizing: border-box;
}

// 预加载状态，防止首次渲染闪烁
.nav-item__icon {
  opacity: 1;
  transition: opacity 0.1s ease;
}

// 针对不同设备的优化
@media (max-width: 320px) {
  .nav-item {
    padding: 4px 3px;

    &__text {
      font-size: @font-size-11; // 使用design-system变量
    }
  }
}

// 高分辨率屏幕优化
@media (-webkit-min-device-pixel-ratio: 2) {
  .nav-item__icon {
    image-rendering: -webkit-optimize-contrast;
  }
}
</style>
