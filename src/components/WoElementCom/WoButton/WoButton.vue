<template>
  <div
    class="wo-button"
    :class="[
      `wo-button-${type}`,
      { 'wo-button-block': block },
      { 'wo-button-disabled': disabled },
      `wo-button-size-${size}`,
      { 'wo-button-round': round }
    ]"
    @click.stop="handleClick"
  >
    <slot></slot>
  </div>
</template>

<script setup>
const props = defineProps({
  type: {
    type: String,
    default: 'default', // primary, secondary, tertiary, danger, text, gradient, cancel
    validator: (value) => ['primary', 'secondary', 'tertiary', 'danger', 'text', 'gradient', 'cancel', 'default'].includes(value)
  },
  size: {
    type: String,
    default: 'large', // xlarge, large, medium, small, mini
    validator: (value) => ['xlarge', 'large', 'medium', 'small'].includes(value)
  },
  block: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  round: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const handleClick = (event) => {
  if (!props.disabled) {
    emit('click', event)
  }
}
</script>

<style scoped lang="less">
.wo-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  text-align: center;
  border: none;
  cursor: pointer;
  border-radius: @radius-9999;
  font-weight: @font-weight-500;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;

  // 类型样式.
  &-default {
    background-color: @bg-color-white;
    color: @text-color-primary;
    border: 1px solid rgba(198,201,204,1);
    //&:active {
    //  background-color: darken(@theme-color, 10%);
    //}
  }

  &-primary {
    background-color: @theme-color;
    color: @bg-color-white;

    &:active {
      background-color: darken(@theme-color, 10%);
    }
  }

  &-secondary {
    background-color: @bg-color-white;
    color: @theme-color;
    border: 1px solid @theme-color;

    &:active {
      background-color: rgba(255, 122, 10, 0.1);
    }
  }

  &-tertiary {
    background-color: rgba(255, 122, 10, 0.1);
    color: @theme-color;

    &:active {
      background-color: rgba(255, 122, 10, 0.2);
    }
  }

  &-danger {
    background-color: @color-red;
    color: @bg-color-white;

    &:active {
      background-color: darken(@color-red, 10%);
    }
  }

  &-text {
    background-color: transparent;
    color: @theme-color;
    padding: 0;

    &:active {
      opacity: @opacity-07;
    }
  }

  &-gradient {
    background-image: @gradient-orange-106;
    color: @bg-color-white;

    &:active {
      background-image: @gradient-orange-dark;
    }
  }

  &-cancel {
    background-color: @bg-color-gray;
    color: @text-color-tertiary;

    &:active {
      background-color: darken(@bg-color-gray, 5%);
    }
  }

  // 尺寸样式
  &-size-xlarge {
    height: @button-height-42;
    font-size: @font-size-16;
    width: 100%;
    border-radius: @radius-22;
  }

  &-size-large {
    height: @button-height-36;
    font-size: @font-size-15;
    width: @button-width-160;
    border-radius: @radius-22;
  }

  &-size-medium {
    height: @button-height-36;
    font-size: @font-size-15;
    width: @button-width-90;
    border-radius: @radius-18;
  }

  &-size-small {
    height: @button-height-28;
    font-size: @font-size-12;
    width: @button-width-80;
    border-radius: @radius-15;
  }

  &-size-special {
    height: @button-height-38;
    font-size: @font-size-15;
    width: @button-width-119;
    border-radius: @radius-20;
  }

  // 块级按钮
  &-block {
    width: 100%;
  }

  // 圆角按钮
  &-round {
    border-radius: @radius-50;
  }

  // 禁用状态
  &-disabled {
    opacity: @opacity-05;
    cursor: not-allowed;

    //&:active {
    //  background-color: inherit;
    //  background-image: inherit;
    //}
  }
}
</style>
