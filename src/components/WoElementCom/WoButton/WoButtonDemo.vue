<template>
  <div class="button-demo">
    <h1 class="demo-title">按钮 / Button</h1>
    <p class="demo-desc">按钮用于触发一个操作，如提交表单，确认操作等</p>

    <section class="demo-section">
      <h2 class="section-title">01_按钮类型</h2>
      <p class="section-desc">常见的按钮主要有七种类型</p>

      <div class="demo-block">
        <div class="button-row">
          <WoButton type="primary">按钮文案</WoButton>
          <span class="desc">主按钮 - primary</span>
        </div>

        <div class="button-row">
          <WoButton type="secondary">按钮文案</WoButton>
          <span class="desc">次按钮 - secondary</span>
        </div>

        <div class="button-row">
          <WoButton type="tertiary">按钮文案</WoButton>
          <span class="desc">辅助按钮 - tertiary</span>
        </div>

        <div class="button-row">
          <WoButton type="danger">危险操作</WoButton>
          <span class="desc">危险按钮 - danger</span>
        </div>

        <div class="button-row">
          <WoButton type="text">文本</WoButton>
          <span class="desc">文本按钮 - text</span>
        </div>

        <div class="button-row">
          <WoButton type="gradient">渐变按钮</WoButton>
          <span class="desc">渐变按钮 - gradient</span>
        </div>

        <div class="button-row">
          <WoButton type="cancel">取消</WoButton>
          <span class="desc">取消按钮 - cancel</span>
        </div>
      </div>
    </section>

    <section class="demo-section">
      <h2 class="section-title">02_按钮尺寸</h2>
      <p class="section-desc">提供四种尺寸，适应不同场景</p>

      <div class="demo-block">
        <div class="button-row">
          <WoButton size="xlarge">超大尺寸</WoButton>
          <span class="desc">超大尺寸 - xlarge</span>
        </div>

        <div class="button-row">
          <WoButton size="large">大尺寸</WoButton>
          <span class="desc">大尺寸 - large</span>
        </div>

        <div class="button-row">
          <WoButton size="medium">中尺寸</WoButton>
          <span class="desc">中尺寸 - medium</span>
        </div>

        <div class="button-row">
          <WoButton size="small">小尺寸</WoButton>
          <span class="desc">小尺寸 - small</span>
        </div>
      </div>
    </section>

    <section class="demo-section">
      <h2 class="section-title">03_按钮状态</h2>
      <p class="section-desc">按钮的不同状态展示</p>

      <div class="demo-block">
        <div class="button-row">
          <WoButton type="primary">正常状态</WoButton>
          <span class="desc">正常状态</span>
        </div>

        <div class="button-row">
          <WoButton type="primary" disabled>禁用状态</WoButton>
          <span class="desc">禁用状态</span>
        </div>

        <div class="button-row">
          <WoButton type="primary" round>圆角按钮</WoButton>
          <span class="desc">圆角按钮</span>
        </div>

        <div class="button-row">
          <WoButton type="primary" block>块级按钮</WoButton>
          <span class="desc">块级按钮</span>
        </div>
      </div>
    </section>

    <section class="demo-section">
      <h2 class="section-title">04_按钮组合场景</h2>

      <div class="demo-block">
        <h3 class="block-title">主按钮</h3>
        <div class="button-layout">
          <WoButton type="primary">按钮文案</WoButton>
        </div>

        <h3 class="block-title">主次按钮/左右摆放</h3>
        <div class="button-layout">
          <div class="button-group">
            <WoButton type="secondary">按钮文案</WoButton>
            <WoButton type="primary">按钮文案</WoButton>
          </div>
        </div>

        <h3 class="block-title">主次按钮/上下摆放</h3>
        <div class="button-layout vertical">
          <WoButton type="primary">按钮文案</WoButton>
          <WoButton type="secondary">按钮文案</WoButton>
        </div>

        <h3 class="block-title">文字/主按钮/左右摆放</h3>
        <div class="button-layout">
          <div class="button-group">
            <WoButton type="text">按钮文案</WoButton>
            <WoButton type="primary">按钮文案</WoButton>
          </div>
        </div>

        <h3 class="block-title">标题位置/左右摆放</h3>
        <div class="button-layout">
          <div class="button-group with-text">
            <span class="text">文字说明/主标题/布局</span>
            <WoButton type="primary">按钮文案</WoButton>
          </div>
        </div>

        <h3 class="block-title">底部按钮/全宽</h3>
        <div class="button-layout">
          <WoButton type="primary" block>按钮文案</WoButton>
        </div>

        <h3 class="block-title">Icon/文字配合/左右摆放</h3>
        <div class="button-layout">
          <div class="cart-button">
            <div class="icon">🛒</div>
            <div class="price">
              <div class="currency">¥</div>
              <div class="amount">1234.24</div>
            </div>
            <WoButton type="primary" class="action-button">文案</WoButton>
          </div>
        </div>

        <h3 class="block-title">多按钮组合</h3>
        <div class="button-layout">
          <div class="multi-button-group">
            <WoButton type="cancel" size="medium">取消</WoButton>
            <WoButton type="secondary" size="medium">保存草稿</WoButton>
            <WoButton type="primary" size="medium">立即发布</WoButton>
          </div>
        </div>
      </div>
    </section>

    <section class="demo-section">
      <h2 class="section-title">05_交互演示</h2>
      <p class="section-desc">点击按钮查看交互效果</p>

      <div class="demo-block">
        <div class="button-row">
          <WoButton type="primary" @click="handleClick('primary')">点击我</WoButton>
          <span class="desc">点击次数: {{ clickCount.primary }}</span>
        </div>

        <div class="button-row">
          <WoButton type="secondary" @click="handleClick('secondary')">点击我</WoButton>
          <span class="desc">点击次数: {{ clickCount.secondary }}</span>
        </div>

        <div class="button-row">
          <WoButton type="text" @click="handleClick('text')">点击我</WoButton>
          <span class="desc">点击次数: {{ clickCount.text }}</span>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'

const clickCount = reactive({
  primary: 0,
  secondary: 0,
  text: 0
})

const handleClick = (type) => {
  clickCount[type]++
  console.log(`${type} button clicked ${clickCount[type]} times`)
}
</script>

<style scoped lang="less">
.button-demo {
  padding: 20px;
  background-color: @bg-color-white;
  max-width: 800px;
  margin: 0 auto;

  .demo-title {
    font-size: @font-size-22;
    font-weight: @font-weight-600;
    margin-bottom: 10px;
    color: @text-color-primary;
  }

  .demo-desc {
    font-size: @font-size-14;
    color: @text-color-secondary;
    margin-bottom: 30px;
  }

  .demo-section {
    margin-bottom: 40px;

    .section-title {
      font-size: @font-size-18;
      font-weight: @font-weight-600;
      margin-bottom: 10px;
      color: @text-color-primary;
    }

    .section-desc {
      font-size: @font-size-14;
      color: @text-color-secondary;
      margin-bottom: 20px;
    }
  }

  .demo-block {
    background-color: @bg-color-gray;
    border-radius: @radius-8;
    padding: 20px;

    .block-title {
      font-size: @font-size-16;
      margin: 15px 0;
      color: @text-color-secondary;
    }

    .button-row {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .desc {
        margin-left: 20px;
        font-size: @font-size-14;
        color: @text-color-tertiary;
      }
    }

    .button-layout {
      margin-bottom: 30px;

      &.vertical {
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-width: 300px;
      }

      .button-group {
        display: flex;
        align-items: center;
        gap: 10px;

        &.with-text {
          justify-content: space-between;
          width: 100%;

          .text {
            font-size: @font-size-14;
            color: @text-color-primary;
          }
        }
      }

      .multi-button-group {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
      }

      .cart-button {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        padding: 10px;
        border-bottom: 1px solid @divider-color-base;

        .icon {
          font-size: @font-size-22;
          margin-right: 10px;
        }

        .price {
          display: flex;
          align-items: baseline;
          color: @theme-color;
          flex-grow: 1;

          .currency {
            font-size: @font-size-12;
          }

          .amount {
            font-size: @font-size-18;
            font-weight: @font-weight-600;
          }
        }

        .action-button {
          margin-left: 10px;
        }
      }
    }
  }
}
</style>
