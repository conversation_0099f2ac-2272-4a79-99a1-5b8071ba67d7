<template>
  <div class="wo-button-docs">
    <div class="docs-header">
      <h1>WoButton 按钮组件</h1>
      <p class="description">按钮用于触发一个操作，如提交表单、确认操作等。支持多种类型、尺寸和状态。</p>
    </div>

    <div class="docs-section">
      <h2>基础用法</h2>
      <div class="example">
        <WoButton>默认按钮</WoButton>
      </div>
      <div class="code-block">
        <pre><code>&lt;WoButton&gt;默认按钮&lt;/WoButton&gt;</code></pre>
      </div>
    </div>

    <div class="docs-section">
      <h2>按钮类型</h2>
      <div class="example">
        <div class="button-group">
          <WoButton type="primary">主要按钮</WoButton>
          <WoButton type="secondary">次要按钮</WoButton>
          <WoButton type="tertiary">辅助按钮</WoButton>
          <WoButton type="danger">危险按钮</WoButton>
          <WoButton type="text">文本按钮</WoButton>
          <WoButton type="gradient">渐变按钮</WoButton>
          <WoButton type="cancel">取消按钮</WoButton>
        </div>
      </div>
    </div>

    <div class="docs-section">
      <h2>按钮尺寸</h2>
      <div class="example">
        <div class="button-group vertical">
          <WoButton size="xlarge">超大按钮</WoButton>
          <WoButton size="large">大按钮</WoButton>
          <WoButton size="medium">中等按钮</WoButton>
          <WoButton size="small">小按钮</WoButton>
        </div>
      </div>
    </div>

    <div class="docs-section">
      <h2>按钮状态</h2>
      <div class="example">
        <div class="button-group">
          <WoButton>正常状态</WoButton>
          <WoButton disabled>禁用状态</WoButton>
        </div>
      </div>
    </div>

    <div class="docs-section">
      <h2>块级按钮</h2>
      <div class="example">
        <WoButton block>块级按钮</WoButton>
      </div>
    </div>

    <div class="docs-section">
      <h2>圆角按钮</h2>
      <div class="example">
        <div class="button-group">
          <WoButton>默认圆角</WoButton>
          <WoButton round>圆形按钮</WoButton>
        </div>
      </div>
    </div>

    <div class="docs-section">
      <h2>API</h2>
      <div class="api-table">
        <h3>Props</h3>
        <table>
          <thead>
            <tr>
              <th>参数</th>
              <th>说明</th>
              <th>类型</th>
              <th>可选值</th>
              <th>默认值</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>type</td>
              <td>按钮类型</td>
              <td>String</td>
              <td>primary / secondary / tertiary / danger / text / gradient / cancel</td>
              <td>primary</td>
            </tr>
            <tr>
              <td>size</td>
              <td>按钮尺寸</td>
              <td>String</td>
              <td>xlarge / large / medium / small</td>
              <td>large</td>
            </tr>
            <tr>
              <td>block</td>
              <td>是否为块级按钮</td>
              <td>Boolean</td>
              <td>true / false</td>
              <td>false</td>
            </tr>
            <tr>
              <td>disabled</td>
              <td>是否禁用</td>
              <td>Boolean</td>
              <td>true / false</td>
              <td>false</td>
            </tr>
            <tr>
              <td>round</td>
              <td>是否为圆形按钮</td>
              <td>Boolean</td>
              <td>true / false</td>
              <td>false</td>
            </tr>
          </tbody>
        </table>

        <h3>Events</h3>
        <table>
          <thead>
            <tr>
              <th>事件名</th>
              <th>说明</th>
              <th>回调参数</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>click</td>
              <td>点击按钮时触发</td>
              <td>event: Event</td>
            </tr>
          </tbody>
        </table>

        <h3>Slots</h3>
        <table>
          <thead>
            <tr>
              <th>名称</th>
              <th>说明</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>default</td>
              <td>按钮内容</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import WoButton from './WoButton.vue'
</script>

<style scoped lang="less">
.wo-button-docs {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  font-family: @font-family-base;

  .docs-header {
    margin-bottom: 32px;

    h1 {
      font-size: @font-size-22;
      font-weight: @font-weight-600;
      color: @text-color-primary;
      margin-bottom: 8px;
    }

    .description {
      font-size: @font-size-14;
      color: @text-color-secondary;
      line-height: 1.6;
    }
  }

  .docs-section {
    margin-bottom: 40px;

    h2 {
      font-size: @font-size-18;
      font-weight: @font-weight-600;
      color: @text-color-primary;
      margin-bottom: 16px;
    }

    h3 {
      font-size: @font-size-16;
      font-weight: @font-weight-500;
      color: @text-color-primary;
      margin: 24px 0 12px 0;
    }
  }

  .example {
    padding: 24px;
    background-color: @bg-color-gray;
    border-radius: @radius-8;
    margin-bottom: 16px;

    .button-group {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;

      &.vertical {
        flex-direction: column;
        align-items: flex-start;
      }
    }
  }

  .code-block {
    background-color: #f6f8fa;
    border: 1px solid #e1e4e8;
    border-radius: @radius-4;
    padding: 16px;

    pre {
      margin: 0;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: @font-size-13;
      color: @text-color-primary;
      overflow-x: auto;
    }
  }

  .api-table {
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 24px;

      th, td {
        padding: 12px 16px;
        text-align: left;
        border-bottom: 1px solid @divider-color-base;
        font-size: @font-size-14;
      }

      th {
        background-color: @bg-color-gray;
        font-weight: @font-weight-500;
        color: @text-color-primary;
      }

      td {
        color: @text-color-secondary;

        &:first-child {
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          color: @theme-color;
          font-weight: @font-weight-500;
        }
      }
    }
  }
}
</style>
