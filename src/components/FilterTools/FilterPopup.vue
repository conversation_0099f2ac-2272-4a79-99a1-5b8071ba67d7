<template>
  <van-popup
    v-model:show="isVisible"
    closeable
    position="right"
    :close-on-click-overlay="true"
    :style="{ width: '80%',height:'100vh',overflow:'hidden' }"
    @open="popupOpen"
    @close="popupClose"
  >
    <div class="filter-area">
      <div class="filter-area-content">
        <div class="filter-area-base">
          <div class="filter-area-address">
            <div class="filter-area-address-left">
              <img class="filter-area-address-icon" src="@/static/images/address-icon.png" alt="">
              <span class="filter-area-address-title">{{locationText}}</span>
            </div>
            <div class="filter-area-address-right" @click="setSwitchAddressPopupShow">
              <span class="filter-area-address-op">切换地址</span>
            </div>
          </div>
        </div>
        <div class="filter-area-base">
          <h3 class="title">服务</h3>
          <ul class="filter-criteria-list">
            <li class="condition" :class="{'active':filterCriteria.isStock}" @click="onIsStock">仅看有货</li>
          </ul>
        </div>
        <div class="filter-area-base">
          <h3 class="title">价格区间</h3>
          <div class="price-range">
            <van-field v-model="filterCriteria.minPrice" maxlength="7" type="digit" label="" placeholder="最低价格" class="input-number" @blur="onFilterCriteriaPriceBlur"/>
            <div class="input-slicing"></div>
            <van-field v-model="filterCriteria.maxPrice" maxlength="7" type="digit" label="" placeholder="最高价格" class="input-number" @blur="onFilterCriteriaPriceBlur"/>
          </div>
        </div>
        <div class="filter-area-base" v-if="filterCriteria.brandsList.length > 0">
          <h3 class="title">品牌</h3>
          <ul class="filter-criteria">
            <li class="condition" :class="{'active':item.isSelected}" v-for="(item,index) in filterCriteria.brandsList" :key="index" @click="onBrandSelect(item)">{{item.value}}</li>
          </ul>
        </div>
      </div>
    </div>
    <div class="filter-area-operator">
      <WoButton
        type="secondary"
        size="medium"
        @click="filterResetBtnClick"
      >
        重置
      </WoButton>
      <WoButton
        type="primary"
        size="medium"
        @click="filterConfirmBtnClick"
      >
        确定
      </WoButton>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import { skuPageListBrandList } from '@/api/index.js'
import { getBizCode } from '@utils/curEnv.js'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  locationText: {
    type: String,
    default: ''
  },
  categoryId: {
    type: String,
    default: ''
  },
  modelValue: {
    type: Object,
    default: () => ({
      isStock: false,
      minPrice: '',
      maxPrice: '',
      brandsList: []
    })
  }
})

// Emits
const emit = defineEmits(['update:show', 'update:modelValue', 'switch-address', 'confirm', 'reset'])

// 内部状态
const isVisible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const filterCriteria = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 添加品牌列表loading状态
const brandsLoading = ref(false)
// 添加品牌数据缓存标识
const brandsDataLoaded = ref(false)

// 添加备份的筛选条件，用于取消时恢复
const backupFilterCriteria = ref({
  isStock: false,
  minPrice: '',
  maxPrice: '',
  brandsList: []
})

// 获取品牌列表的方法
const fetchBrandsList = async () => {
  if (brandsDataLoaded.value) {
    return
  }
  brandsLoading.value = true
  try {
    const [err, data] = await skuPageListBrandList({
      bizCode: getBizCode(),
      categoryId: props.categoryId
    })

    if (!err && data) {
      const brandsList = data.map(brand => ({
        value: brand.name || brand.value || brand,
        isSelected: false
      }))

      const newFilterCriteria = { ...filterCriteria.value }
      newFilterCriteria.brandsList = brandsList
      emit('update:modelValue', newFilterCriteria)

      backupFilterCriteria.value.brandsList = JSON.parse(JSON.stringify(brandsList))
      brandsDataLoaded.value = true
    }
  } catch (error) {
    console.error('获取品牌列表失败:', error)
  } finally {
    brandsLoading.value = false
  }
}

// 弹窗打开事件
const popupOpen = () => {
  if (!brandsDataLoaded.value || filterCriteria.value.brandsList.length === 0) {
    fetchBrandsList()
  }
}

// 弹窗关闭事件
const popupClose = () => {
  restoreFilterCriteria()
}

// 恢复筛选条件的方法
const restoreFilterCriteria = () => {
  const restored = {
    isStock: backupFilterCriteria.value.isStock,
    minPrice: backupFilterCriteria.value.minPrice,
    maxPrice: backupFilterCriteria.value.maxPrice,
    brandsList: filterCriteria.value.brandsList.map((brand, index) => ({
      ...brand,
      isSelected: backupFilterCriteria.value.brandsList[index]?.isSelected || false
    }))
  }
  emit('update:modelValue', restored)
}

// 备份当前筛选条件
const backupCurrentFilterCriteria = () => {
  backupFilterCriteria.value = {
    isStock: filterCriteria.value.isStock,
    minPrice: filterCriteria.value.minPrice,
    maxPrice: filterCriteria.value.maxPrice,
    brandsList: JSON.parse(JSON.stringify(filterCriteria.value.brandsList))
  }
}

// 设置地址切换弹窗显示状态
const setSwitchAddressPopupShow = () => {
  emit('switch-address')
}

// 切换仅看有货状态
const onIsStock = () => {
  const newFilterCriteria = { ...filterCriteria.value }
  newFilterCriteria.isStock = !newFilterCriteria.isStock
  emit('update:modelValue', newFilterCriteria)
}

// 价格输入失焦事件
const onFilterCriteriaPriceBlur = () => {
  const minPrice = parseFloat(filterCriteria.value.minPrice)
  const maxPrice = parseFloat(filterCriteria.value.maxPrice)

  if (minPrice && maxPrice && minPrice > maxPrice) {
    const newFilterCriteria = { ...filterCriteria.value }
    const temp = newFilterCriteria.minPrice
    newFilterCriteria.minPrice = newFilterCriteria.maxPrice
    newFilterCriteria.maxPrice = temp
    emit('update:modelValue', newFilterCriteria)
  }
}

// 品牌选择
const onBrandSelect = (brand) => {
  brand.isSelected = !brand.isSelected
  emit('update:modelValue', filterCriteria.value)
}

// 重置筛选条件
const filterResetBtnClick = () => {
  const resetCriteria = {
    isStock: false,
    minPrice: '',
    maxPrice: '',
    brandsList: filterCriteria.value.brandsList.map(brand => ({
      ...brand,
      isSelected: false
    }))
  }
  emit('update:modelValue', resetCriteria)
  emit('reset')
}

// 确认筛选
const filterConfirmBtnClick = () => {
  backupCurrentFilterCriteria()
  emit('confirm', filterCriteria.value)
  isVisible.value = false
}
</script>

<style scoped lang="less">
.filter-area {
  position: relative;
  height: 80vh;
  margin-top: @padding-page * 10;
  padding: 0 @padding-page * 4 0 @padding-page * 4;
  overflow: scroll;

  .filter-area-content {
    .filter-area-base {
      margin-bottom: 20px;

      .title {
        margin-bottom: @padding-page * 2;
        font-size: @font-size-15;
        color: @text-color-primary;
        line-height: @font-size-15;
        font-weight: @font-weight-500;
      }

      .price-range {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .input-number {
          background: @bg-color-gray;
          border-radius: @radius-4;
          height: @button-height-24 + 1px;
          line-height: @button-height-24 + 1px;
          :deep(.van-field__control) {
            text-align: center !important;
            padding: 0 @padding-page !important;
          }
        }

        :deep(.van-cell) {
          padding: 0;
        }

        .input-slicing {
          width: @padding-page * 4;
          height: 2px !important;
          margin: 0 @padding-page * 2;
          background: rgba(177, 190, 201, 1);
        }
      }

      .filter-criteria {
        display: grid;
        grid-template-rows: repeat(3, 1fr);
        grid-template-columns: repeat(3, 1fr);
        grid-gap: @padding-page * 2;

        .condition {
          height: @button-height-24 + 1px;
          line-height: @button-height-24 + 1px;
          text-align: center;
          padding: 0 @padding-page * 2;
          font-size: @font-size-11 - 1px;
          color: @text-color-secondary;
          font-weight: @font-weight-400;
          background: @bg-color-gray;
          border-radius: @radius-4;
          .ellipsis();

          &.active {
            color: @text-color-white;
            background-image: @gradient-orange-106;
          }
        }
      }

      .filter-criteria-list {
        display: grid;
        grid-template-rows: repeat(1, 1fr);
        grid-template-columns: repeat(3, 1fr);
        grid-gap: @padding-page * 2;

        .condition {
          height: @button-height-24 + 1px;
          line-height: @button-height-24 + 1px;
          text-align: center;
          padding: 0 @padding-page * 2;
          font-size: @font-size-11 - 1px;
          color: @text-color-secondary;
          font-weight: @font-weight-400;
          background: @bg-color-gray;
          border-radius: @radius-4;
          .ellipsis();

          &.active {
            color: @text-color-white;
            background-image: @gradient-orange-106;
          }
        }
      }

      .filter-area-address {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: @padding-page * 2;
        font-size: @font-size-13;
        line-height: 1.6;
        color: @text-color-primary;

        .filter-area-address-left {
          flex: 1;
          margin-right: @padding-page * 2;
          align-items: center;
          .ellipsis();

          .filter-area-address-icon {
            width: 14px;
            height: @font-size-15;
            vertical-align: middle;
          }

          .filter-area-address-title {
            margin-left: @padding-page;
            font-weight: @font-weight-700;
          }
        }

        .filter-area-address-right {
          .filter-area-address-op {
            color: @theme-color;
            font-weight: @font-weight-500;
          }
        }
      }
    }
  }
}

.filter-area-operator {
  padding: 0 @padding-page * 4;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  height: 65px;
  background: @bg-color-white;
  box-sizing: border-box;
}
</style>
