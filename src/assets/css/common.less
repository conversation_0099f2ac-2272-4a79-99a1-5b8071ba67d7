@import 'reset.css';
@import "./design-system.less";
// 加载WOOFF格式的字体
@font-face {
  font-family: 'D-DIN-PRO SemiBold';  // 自定义字体名称
  font-style: normal;        // 常规样式
  src: url('../fonts/D-DIN-PRO-600-SemiBold.woff2') format('woff2'); // 路径根据实际位置调整
}

// 公共的loading
.wo-loading {
  box-sizing: border-box;
  padding: 5px !important;
  min-width: 88px !important;
  min-height: 88px !important;
  background-color: #fff !important;
  box-shadow: 0 0 10px 0 #ccc !important;
  border-radius: 10px !important;
  color: #000;

  .van-toast__icon {
    .van-icon__image {
      width: 64px !important;
      height: 64px !important;
    }
  }

  .van-toast__text {
    padding-bottom: 8px !important;
  }
  &.submit-loading {
    box-shadow: none !important;

    &:after {
      content: '正在提交，请耐心等待';
      position: absolute;
      width: 200px;
      color: #fff;
    }
  }
}
// 去除滚动条
::-webkit-scrollbar {
  width: 0 !important;
}
::-webkit-scrollbar {
  width: 0 !important;
  height: 0;
}

.more-popover-content .van-popover__content {
  border-radius: @radius-4;
}

.more-popover-content .van-popover__action {
  padding: 0 10px;
  width: 85px;
  height: 30px;
  font-size: 12px;
}

.custom-back-top.van-floating-bubble {
  height: 100px;
  background-color: transparent !important;
  border: none !important;
  border-radius: 0!important;
  box-shadow: none !important;
  // 去除点击样式
  &:active {
    opacity: 1 !important;
    background-color: transparent !important;
  }

  // 去除点击涟漪效果
  &::before {
    display: none !important;
  }
}
