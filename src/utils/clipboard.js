import { showToast } from 'vant'
import useClipboard from 'vue-clipboard3'

/**
 * 复制文本到剪贴板，并使用 vant Toast 提示
 * @param {string} text 要复制的文本
 * @returns {Promise<boolean>} 复制是否成功
 */
export const copy = async (text) => {
  const { toClipboard } = useClipboard()
  try {
    await toClipboard(text)
    showToast('链接已复制，赶快分享吧！~')
    return true
  } catch (e) {
    showToast('设备不支持复制功能，请手动复制...')
    return false
  }
}
