// 定义状态枚举，用于表示图片压缩的不同状态
const STATUS = {
  NOT_REACH_COMPRESS_THRESHOLD: 0, // 文件大小未达到压缩阈值
  COMPRESS_SUCCESS: 1, // 图片压缩成功
  NOT_IMAGE_FILE: 2, // 非图片文件不进行压缩
  NOT_SUPPORT_COMPRESS: 3, // 当前浏览器不支持图片压缩
  COMPRESS_ERROR: 4 // 图片压缩过程出错
}

// 定义状态映射消息对象，用于将状态码映射为具体的消息
const STATUS_MAPPING_MSG = {
  0: '文件大小未达到压缩阈值',
  1: '图片压缩成功',
  2: '非图片文件不进行压缩',
  3: '当前浏览器不支持图片压缩',
  4: '图片压缩过程出错'
}

// 压缩图片文件函数
const compressImageFile = (props) => {
  const {
    file, // 待压缩的文件对象
    threshold = 2, // 压缩阈值，默认为2MB
    quality = 0.5, // 压缩质量，默认为0.5
    shrink = false, // 是否需要缩放图片
    shrinkMaxWidth = 1920, // 最大宽度，默认1920px
    shrinkMaxHeight = 1080, // 最大高度，默认1080px
    compressUntilSizeBelowThreshold = false // 是否压缩直到文件大小低于阈值
  } = props

  const isAccept = [
    'image/png',
    'image/jpeg',
    'image/jpg',
    'image/gif',
    'image/bmp',
    'image/tiff',
    'image/webp',
    'image/svg+xml',
    'image/heic'
  ]

  // 使用 indexOf 方法检查 file.type 是否在 isAccept 数组中
  const isFileTypeAccepted = isAccept.indexOf(file.type) !== -1
  // 判断图片类型，非图片文件，不进行处理，直接返回原 file 对象和提示信息
  if (!isFileTypeAccepted) {
    return Promise.resolve({
      file: file,
      isCompressed: false,
      status: STATUS.NOT_IMAGE_FILE,
      msg: STATUS_MAPPING_MSG[STATUS.NOT_IMAGE_FILE]
    })
  }

  const fileSize = file.size / 1024 / 1024

  // 判断文件大小是否达到压缩阈值，如果小于阈值，不需要压缩，直接返回原 file 对象和提示信息
  if (fileSize < threshold) {
    return Promise.resolve({
      file: file,
      isCompressed: false,
      status: STATUS.NOT_REACH_COMPRESS_THRESHOLD,
      msg: STATUS_MAPPING_MSG[STATUS.NOT_REACH_COMPRESS_THRESHOLD]
    })
  }

  // 判断浏览器内核是否支持 base64 图片压缩，不支持压缩，直接返回原 file 对象和提示信息
  if (typeof FileReader === 'undefined') {
    return Promise.resolve({
      file: file,
      isCompressed: false,
      status: STATUS.NOT_SUPPORT_COMPRESS,
      msg: STATUS_MAPPING_MSG[STATUS.NOT_SUPPORT_COMPRESS]
    })
  }

  // 定义压缩过程出错的返回数据
  const compressErrorContent = {
    file: file,
    isCompressed: false,
    status: STATUS.COMPRESS_ERROR,
    msg: STATUS_MAPPING_MSG[STATUS.COMPRESS_ERROR]
  }

  return new Promise((resolve) => {
    try {
      // 声明 FileReader 文件读取对象
      const reader = new FileReader()
      reader.readAsDataURL(file) // 将文件内容读取为一个 Base64 编码的字符串，并将其表示为 Data URL
      reader.onload = (event) => {
        // 生成img
        const img = new Image()
        img.src = event.target?.result || reader?.result || ''
        img.onload = () => {
          // 生成canvas画布
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          // 不支持canvas，直接返回原 file 对象和提示信息
          if (!ctx) {
            return resolve(compressErrorContent)
          }
          // 原始图片宽度、高度
          const originImageWidth = img.width
          const originImageHeight = img.height
          // 默认最大尺度的尺寸限制在（1920 * 1080）
          const ratio = (shrinkMaxWidth || 1920) / (shrinkMaxHeight || 1080)
          // 目标尺寸
          let targetWidth = originImageWidth
          let targetHeight = originImageHeight
          // 需要剪裁时，当图片的宽度或者高度大于指定的最大宽度或者最大高度时，进行缩放图片
          if (
            shrink &&
            (originImageWidth > shrinkMaxWidth || originImageHeight > shrinkMaxHeight)
          ) {
            // 原始图片宽高比例和定义的最大宽高比例进行比较
            if (originImageWidth / originImageHeight > ratio) {
              // 如果是宽度超了，宽度取最大宽度值maxWidth，按原始比例缩放高度，以保证不变形
              targetWidth = shrinkMaxWidth
              targetHeight = Math.round(shrinkMaxWidth * (originImageHeight / originImageWidth))
            } else {
              // 如果是高度超了，高度取最大高度值maxHeight，按原始比例缩放宽度，以保证不变形
              targetHeight = shrinkMaxHeight
              targetWidth = Math.round(shrinkMaxHeight * (originImageWidth / originImageHeight))
            }
          }
          // 设置canvas画板的为目标宽高
          canvas.width = targetWidth
          canvas.height = targetHeight
          // 清除画布
          ctx.clearRect(0, 0, targetWidth, targetHeight)
          // 绘制图片，需要剪裁且超过设定宽高时目标尺寸是按照原始图片宽高比例缩放后的尺寸
          ctx.drawImage(img, 0, 0, targetWidth, targetHeight)
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const newFile = new File([blob], file.name, { type: file.type })
                // 如果开启压缩大小在阈值之下，当前文件大小大于等于阈值，继续压缩质量高于0.2，允许继续压缩
                if (
                  compressUntilSizeBelowThreshold &&
                  newFile.size / 1024 / 1024 >= threshold &&
                  quality - 0.2 >= 0.2
                ) {
                  // 继续压缩，直到文件大小低于阈值，图片质量会逐渐减低，压缩时间会比较长
                  compressImageFile({
                    file: newFile,
                    threshold: threshold,
                    quality: quality - 0.2, // 图片质量递减 0.2
                    shrink: shrink,
                    shrinkMaxWidth: shrinkMaxWidth,
                    shrinkMaxHeight: shrinkMaxHeight,
                    compressUntilSizeBelowThreshold: compressUntilSizeBelowThreshold
                  }).then((res) => {
                    resolve(res)
                  })
                } else {
                  // 不需要继续压缩，直接返回压缩后的文件对象和提示信息
                  return resolve({
                    file: newFile,
                    isCompressed: true,
                    status: STATUS.COMPRESS_SUCCESS,
                    msg: STATUS_MAPPING_MSG[STATUS.COMPRESS_SUCCESS]
                  })
                }
              } else {
                // 压缩过程出错，直接返回原 file 对象和提示信息
                return resolve(compressErrorContent)
              }
            },
            file.type,
            quality
          )
        }
        img.onerror = () => {
          // 压缩过程出错，直接返回原 file 对象和提示信息
          return resolve(compressErrorContent)
        }
      }
      reader.onerror = () => {
        // 压缩过程出错，直接返回原 file 对象和提示信息
        return resolve(compressErrorContent)
      }
    } catch (error) {
      // 压缩过程出错，直接返回原 file 对象和提示信息
      return resolve(compressErrorContent)
    }
  })
}

export default compressImageFile
