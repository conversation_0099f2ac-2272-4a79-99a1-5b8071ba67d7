import { storage } from 'commonkit'
// 存储相关常量定义
// 分销业务码（原始码）
export const curDistriBiz = storage('PS_CCMS_DISTRI_BIZ')
// 渠道码
export const curChannelBiz = storage('PS_CCMS_CHANNEL_BIZ')
// 发展人id
export const curDeveloperId = storage('PS_CCMS_DEVELOPER_ID')
// 京东开普勒引导层时间控制
export const curJdGuidePageDate = storage('PS_CCMS_JD_GUIDE_PAGE', true)
// 订单确认页分期编码
export const curLoanCode = storage('PS_CCMS_LOAN_CODE')
// 当前商品池 id
export const curClassificationId = storage('PS_CCMS_CLASSIFICATION_ID')
// 扶贫商城荣誉证书图片base64缓存
export const fpCertificateCache = storage('PS_CCMS_FP_CERTIFICATE', true)
// 首页浮标控制
export const indexFloatTag = storage('PS_CCMS_INDEX_FLOAT_TAG')
// 省分助农地址栏获取分类id
export const categoryPid = storage('PS_CCMS_CATEGORY_PID')
// 用户缓存的临时地址
export const curTempAddrInfo = storage('PS_CCMS_TEMP_ADDR_INFO')
// 当前渠道对应登录类型（登录参数使用，0-默认模式，1-查询政企数据模式）
export const loginType = storage('PS_CCMS_LOGIN_TYPE')
// 政企渠道，当前登录角色（通过url参数获取，仅在多角色选择条件下启用）
export const zqRole = storage('PS_CCMS_ZQ_ROLE')
// 商品相关存储
// 立即购买的商品 Local
export const buyProductNow = storage('PS_CCMS_BUY_NOW_PRODUCT', true)
// 购车购买的商品 Local
export const buyProductCart = storage('PS_CCMS_BUY_CART_PRODUCT', true)
// 立即购买的商品 Session
export const buyProductNowSession = storage('PS_CCMS_BUY_NOW_PRODUCT', false)
// 购车购买的商品 Session
export const buyProductCartSession = storage('PS_CCMS_BUY_CART_PRODUCT', false)
// 购车购买的商品 Local
export const afterSalesProduct = storage('PS_CCMS_AFTER_SALES_PRODUCT', false)
