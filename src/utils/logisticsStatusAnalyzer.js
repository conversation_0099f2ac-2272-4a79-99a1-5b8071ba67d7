/**
 * 物流状态语义分析器
 * 根据物流轨迹信息智能分析当前物流状态
 * 集成 compromise 库进行增强语义分析
 */

import nlp from 'compromise'

// 物流状态枚举
export const LOGISTICS_STATUS = {
  PENDING_PICKUP: 'pending_pickup',     // 待揽件
  PICKED_UP: 'picked_up',               // 已揽件
  IN_TRANSIT: 'in_transit',             // 运输中
  OUT_FOR_DELIVERY: 'out_for_delivery', // 派送中
  DELIVERED: 'delivered',               // 已签收
  FAILED_DELIVERY: 'failed_delivery',   // 派送失败
  RETURNED: 'returned',                 // 已退回
  EXCEPTION: 'exception'                // 异常
}

// 状态显示文本映射
export const STATUS_TEXT_MAP = {
  [LOGISTICS_STATUS.PENDING_PICKUP]: '待揽件',
  [LOGISTICS_STATUS.PICKED_UP]: '已揽收',
  [LOGISTICS_STATUS.IN_TRANSIT]: '运输中',
  [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: '派送中',
  [LOGISTICS_STATUS.DELIVERED]: '已签收',
  [LOGISTICS_STATUS.FAILED_DELIVERY]: '派送失败',
  [LOGISTICS_STATUS.RETURNED]: '已退回',
  [LOGISTICS_STATUS.EXCEPTION]: '异常'
}

// 关键词匹配规则 - 按权重分级
const STATUS_KEYWORDS = {
  [LOGISTICS_STATUS.PENDING_PICKUP]: {
    high: ['待揽收', '等待揽收', '待收件', '等待收件', '订单已提交', '已下单待揽收', '等待揽件', '待揽件', '等待取件人', '寄件人已下单'],
    medium: ['已下单', '订单生成', '等待取件', '预约取件', '订单受理', '订单处理中', '等待快递员', '等待上门', '预约上门'],
    low: ['订单信息', '寄件信息', '下单成功', '订单确认', '等待处理']
  },

  [LOGISTICS_STATUS.PICKED_UP]: {
    high: ['已揽收', '揽件成功', '收件成功', '已收件', '揽投员已收件', '快递员已取件', '揽件', '已揽件', '取件成功', '上门收件', '收寄成功'],
    medium: ['已取件', '已从.*收寄', '收寄完成', '揽投员', '已收取', '收件完成', '快递员收件', '业务员收件', '已收货', '收件员', '取件员'],
    low: ['已从', '收寄', '取件', '收货', '收取', '揽收网点', '收件网点']
  },

  [LOGISTICS_STATUS.IN_TRANSIT]: {
    high: ['运输中', '在途中', '运输途中', '正在运输', '已发往.*中转', '离开.*中转', '运输', '在途', '途中', '运送中', '转运途中'],
    medium: ['转运中', '中转', '发往', '离开', '到达.*中转', '经过', '途经', '装车发往', '已装车', '发车', '运往', '送往', '转运', '中转处理'],
    low: ['中转站', '分拣中心', '装车', '发车', '运输', '转运', '分拣', '集散', '处理中心', '转运中心', '集散中心', '分拣完成']
  },

  [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: {
    high: ['派送中', '正在派送', '派件中', '配送中', '正在配送', '开始派送', '派送员.*派送', '出库派送', '安排派送', '派送', '配送'],
    medium: ['投递中', '快递员.*派送', '派送员', '配送员', '正在派件', '派送途中', '准备派送', '派件', '投递', '送件中', '配送途中', '正在投递'],
    low: ['最后一公里', '同城配送', '本地派送', '终端派送', '末端配送', '派送站', '配送站', '投递员']
  },

  [LOGISTICS_STATUS.DELIVERED]: {
    high: ['已签收', '签收成功', '投递成功', '派送成功', '妥投', '本人签收', '签收', '收货签收', '客户签收', '成功签收', '完成签收'],
    medium: ['配送完成', '已投递', '代收', '他人代收', '成功投递', '投递完成', '签收人', '代签收', '家人代收', '同事代收', '邻居代收'],
    low: ['收件人', '快递柜', '驿站', '代签', '菜鸟驿站', '丰巢', '速递易', '近邻宝', '收件', '取件', '自提']
  },

  [LOGISTICS_STATUS.FAILED_DELIVERY]: {
    high: ['派送失败', '投递失败', '配送失败', '无人签收', '拒收', '客户拒收', '派送不成功', '投递不成功', '无法派送', '派送异常'],
    medium: ['地址错误', '联系不上', '电话无人接听', '改派', '延误派送', '无法联系', '地址不详', '收件人不在', '无人接听', '联系失败', '改期配送'],
    low: ['暂存', '改期派送', '预约派送', '地址有误', '电话错误', '信息不全', '待联系', '重新派送']
  },

  [LOGISTICS_STATUS.RETURNED]: {
    high: ['已退回', '退回发件人', '原路返回', '逆向物流', '退件', '退回', '返回发件人', '退货', '原件退回'],
    medium: ['退回', '返回', '退货', '回退', '退回寄件人', '返回原地', '逆向运输', '退回处理'],
    low: ['返回途中', '退回中', '退回运输', '逆向', '回程']
  },

  [LOGISTICS_STATUS.EXCEPTION]: {
    high: ['异常', '问题件', '滞留', '丢失', '破损', '包裹异常', '运输异常', '快件异常', '处理异常', '派送异常', '投递异常'],
    medium: ['超时', '延误', '无法派送', '地址不详', '收件人信息有误', '信息错误', '联系异常', '处理失败', '系统异常', '网络异常'],
    low: ['暂扣', '待处理', '需核实', '待确认', '核实中', '处理中', '查询中', '调查中']
  }
}

/**
 * 分析物流状态
 * @param {Array} orderTrack - 物流轨迹数组，按时间倒序排列
 * @returns {Object} 分析结果
 */
export function analyzeLogisticsStatus(orderTrack) {
  if (!orderTrack || orderTrack.length === 0) {
    return {
      status: LOGISTICS_STATUS.PENDING_PICKUP,
      statusText: STATUS_TEXT_MAP[LOGISTICS_STATUS.PENDING_PICKUP],
      latestTrack: null,
      confidence: 0
    }
  }

  // 获取最新的轨迹记录
  const latestTrack = orderTrack[0]
  const context = latestTrack.context || ''

  // 只分析第一条轨迹的状态
  const analysisResult = analyzeTrackContext(context)

  return {
    ...analysisResult,
    latestTrack
  }
}

/**
 * 分析单条轨迹上下文
 * @param {string} context - 轨迹描述文本
 * @returns {Object} 分析结果
 */
function analyzeTrackContext(context) {
  let maxScore = 0
  let detectedStatus = LOGISTICS_STATUS.PENDING_PICKUP
  let statusScores = {}

  // 遍历所有状态的关键词
  for (const [status, keywords] of Object.entries(STATUS_KEYWORDS)) {
    const score = calculateKeywordScore(context, keywords)
    statusScores[status] = score

    if (score > maxScore) {
      maxScore = score
      detectedStatus = status
    }
  }

  // 先保存原始关键词匹配结果
  const keywordMatchResult = {
    status: detectedStatus,
    score: maxScore,
    confidence: Math.min(maxScore / 50, 1) // 降低阈值要求
  }

  // 语义增强分析
  const semanticResult = enhancedSemanticAnalysis(context)

  // 语义分析加成
  maxScore += semanticResult.semanticScore

  // 使用 compromise 分析结果进行状态修正（但不能完全覆盖明显的关键词匹配）
  const compromiseStatus = predictStatusFromCompromise(semanticResult.compromiseAnalysis, context)
  if (compromiseStatus && compromiseStatus.confidence > 0.7) {
    // 如果关键词匹配得分很高（超过20分），优先相信关键词匹配
    if (keywordMatchResult.score >= 20) {
      // 保持关键词匹配结果，但可以提升置信度
      maxScore += 5
    } else {
      // 关键词匹配得分不高时，考虑 compromise 建议
      if (compromiseStatus.status !== detectedStatus) {
        const currentConfidence = Math.min(maxScore / 50, 1)
        if (compromiseStatus.confidence > currentConfidence) {
          detectedStatus = compromiseStatus.status
          maxScore = compromiseStatus.confidence * 50
        }
      } else {
        // 状态一致时，提升置信度
        maxScore += 8
      }
    }
  }

  // 计算最终置信度
  let confidence = Math.min(maxScore / 50, 1) // 降低阈值要求
  confidence = Math.max(confidence, 0.1)

  return {
    status: detectedStatus,
    statusText: STATUS_TEXT_MAP[detectedStatus],
    confidence,
    rawScore: maxScore,
    keywordScores: statusScores,
    semanticAnalysis: semanticResult
  }
}

/**
 * 基于 compromise 分析结果预测物流状态
 * @param {Object} compromiseAnalysis - compromise 分析结果
 * @param {string} context - 原始上下文
 * @returns {Object|null} 预测结果
 */
function predictStatusFromCompromise(compromiseAnalysis, context) {
  if (!compromiseAnalysis || compromiseAnalysis.semanticBonus === 0) {
    return null
  }

  const { verbs, tenseAnalysis, sentiment, places } = compromiseAnalysis
  let predictedStatus = null
  let confidence = 0.5

  // 基于动词预测状态
  const verbLower = verbs.map(v => v.toLowerCase())

  if (verbLower.some(v => ['delivered', 'signed', 'completed', 'received'].includes(v))) {
    predictedStatus = LOGISTICS_STATUS.DELIVERED
    confidence = 0.9
  } else if (verbLower.some(v => ['delivering', 'dispatching', 'sending'].includes(v))) {
    predictedStatus = LOGISTICS_STATUS.OUT_FOR_DELIVERY
    confidence = 0.8
  } else if (verbLower.some(v => ['picked', 'collected', 'received'].includes(v)) && tenseAnalysis.isPast) {
    predictedStatus = LOGISTICS_STATUS.PICKED_UP
    confidence = 0.8
  } else if (verbLower.some(v => ['moving', 'traveling', 'transporting'].includes(v))) {
    predictedStatus = LOGISTICS_STATUS.IN_TRANSIT
    confidence = 0.7
  } else if (verbLower.some(v => ['failed', 'rejected', 'unable'].includes(v))) {
    predictedStatus = LOGISTICS_STATUS.FAILED_DELIVERY
    confidence = 0.8
  }

  // 基于时态调整预测
  if (tenseAnalysis.isPast && sentiment.polarity > 0) {
    // 过去时态 + 正面情感 = 可能已完成
    if (!predictedStatus) {
      predictedStatus = LOGISTICS_STATUS.DELIVERED
      confidence = 0.6
    } else if (predictedStatus === LOGISTICS_STATUS.DELIVERED) {
      confidence = Math.min(confidence + 0.1, 1)
    }
  } else if (tenseAnalysis.isProgressive) {
    // 进行时态 = 正在进行的动作
    if (!predictedStatus) {
      predictedStatus = LOGISTICS_STATUS.IN_TRANSIT
      confidence = 0.6
    }
  }

  // 基于地点信息调整
  if (places.length > 0) {
    confidence = Math.min(confidence + 0.1, 1)
  }

  // 基于情感极性调整
  if (sentiment.polarity < -1) {
    // 强烈负面情感可能表示问题
    if (predictedStatus === LOGISTICS_STATUS.OUT_FOR_DELIVERY) {
      predictedStatus = LOGISTICS_STATUS.FAILED_DELIVERY
    } else if (!predictedStatus) {
      predictedStatus = LOGISTICS_STATUS.EXCEPTION
      confidence = 0.7
    }
  }

  return predictedStatus ? {
    status: predictedStatus,
    confidence,
    source: 'compromise'
  } : null
}

/**
 * 计算关键词匹配分数 - 支持权重分级和正则匹配
 * @param {string} text - 待分析文本
 * @param {Object} keywords - 分级关键词对象 {high: [], medium: [], low: []}
 * @returns {number} 匹配分数
 */
function calculateKeywordScore(text, keywords) {
  let score = 0
  const lowerText = text.toLowerCase()

  // 权重配置 - 提高权重以确保关键词能被正确识别
  const weights = { high: 30, medium: 15, low: 8 }

  for (const [level, keywordList] of Object.entries(keywords)) {
    const weight = weights[level] || 8

    for (const keyword of keywordList) {
      const lowerKeyword = keyword.toLowerCase()

      // 支持正则表达式匹配
      if (keyword.includes('.*')) {
        try {
          const regex = new RegExp(lowerKeyword, 'i')
          if (regex.test(lowerText)) {
            score += weight * 1.5 // 正则匹配额外加分
          }
        } catch (e) {
          // 正则表达式错误时降级为普通匹配
          const fallbackKeyword = lowerKeyword.replace(/\.\*/g, '')
          if (lowerText.includes(fallbackKeyword)) {
            score += weight
          }
        }
      } else {
        // 普通关键词匹配
        if (lowerText.includes(lowerKeyword)) {
          // 完全匹配额外加分
          if (lowerText.trim() === lowerKeyword) {
            score += weight * 2
          } else {
            score += weight
          }
        }
      }
    }
  }

  return score
}



/**
 * 上下文语义增强分析 - 集成 compromise 库
 * @param {string} context - 轨迹描述
 * @returns {Object} 语义分析结果
 */
function enhancedSemanticAnalysis(context) {
  // 基础模式匹配
  const negativePatterns = [
    '未.*成功', '尚未.*', '暂未.*', '等待.*', '准备.*', '即将.*'
  ]

  const positivePatterns = [
    '已.*完成', '成功.*', '顺利.*', '正常.*'
  ]

  // 新增：语义强度分析
  const strongPositive = ['妥投', '签收成功', '投递完成']
  const strongNegative = ['派送失败', '拒收', '异常', '丢失']

  let semanticScore = 0
  const lowerContext = context.toLowerCase()

  // 使用 compromise 进行语义分析
  const compromiseResult = analyzeWithCompromise(context)

  // 检查强烈肯定词汇
  for (const pattern of strongPositive) {
    if (context.includes(pattern)) {
      semanticScore += 10
    }
  }

  // 检查强烈否定词汇
  for (const pattern of strongNegative) {
    if (context.includes(pattern)) {
      semanticScore -= 10
    }
  }

  // 检查否定词汇
  for (const pattern of negativePatterns) {
    const regex = new RegExp(pattern, 'i')
    if (regex.test(context)) {
      semanticScore -= 5
    }
  }

  // 检查肯定词汇
  for (const pattern of positivePatterns) {
    const regex = new RegExp(pattern, 'i')
    if (regex.test(context)) {
      semanticScore += 5
    }
  }

  // 融合 compromise 分析结果
  semanticScore += compromiseResult.semanticBonus

  // 新增：上下文连贯性分析
  const contextCoherence = analyzeContextCoherence(context)
  semanticScore += contextCoherence

  return {
    semanticScore,
    coherence: contextCoherence,
    intensity: Math.abs(semanticScore) > 8 ? 'high' : 'normal',
    compromiseAnalysis: compromiseResult
  }
}

/**
 * 使用 compromise 库进行语义分析
 * @param {string} context - 轨迹描述
 * @returns {Object} compromise 分析结果
 */
function analyzeWithCompromise(context) {
  try {
    const doc = nlp(context)

    // 提取动词和时态信息
    const verbs = doc.verbs().out('array')
    const pastTense = doc.verbs().toPastTense().out('array')
    const presentTense = doc.verbs().toPresentTense().out('array')

    // 提取名词（地点、人员等）
    const nouns = doc.nouns().out('array')
    const places = doc.places().out('array')
    const people = doc.people().out('array')

    // 情感分析
    const sentiment = analyzeSentimentWithCompromise(doc)

    // 时态分析
    const tenseAnalysis = analyzeTensePattern(doc)

    // 计算语义加成分数
    let semanticBonus = 0

    // 动词完成度分析
    if (verbs.some(verb => ['delivered', 'completed', 'finished', 'signed'].includes(verb.toLowerCase()))) {
      semanticBonus += 8
    }

    // 进行时态加成
    if (tenseAnalysis.isProgressive) {
      semanticBonus += 5
    }

    // 过去时态加成（表示已完成）
    if (tenseAnalysis.isPast) {
      semanticBonus += 6
    }

    // 地点信息加成
    if (places.length > 0) {
      semanticBonus += 3
    }

    // 人员信息加成
    if (people.length > 0) {
      semanticBonus += 2
    }

    // 情感极性加成
    semanticBonus += sentiment.polarity * 3

    return {
      verbs,
      nouns,
      places,
      people,
      sentiment,
      tenseAnalysis,
      semanticBonus: Math.max(-10, Math.min(10, semanticBonus)) // 限制范围
    }
  } catch (error) {
    console.warn('Compromise analysis failed:', error)
    return {
      verbs: [],
      nouns: [],
      places: [],
      people: [],
      sentiment: { polarity: 0, confidence: 0 },
      tenseAnalysis: { isPast: false, isProgressive: false },
      semanticBonus: 0
    }
  }
}

/**
 * 使用 compromise 进行情感分析
 * @param {Object} doc - compromise 文档对象
 * @returns {Object} 情感分析结果
 */
function analyzeSentimentWithCompromise(doc) {
  // 提取形容词进行情感分析
  const adjectives = doc.adjectives().out('array')

  // 定义情感词汇
  const positiveWords = ['successful', 'completed', 'delivered', 'good', 'normal', '成功', '完成', '正常', '顺利']
  const negativeWords = ['failed', 'error', 'problem', 'delayed', 'unable', '失败', '错误', '延误', '异常']

  let positiveScore = 0
  let negativeScore = 0

  // 分析形容词情感倾向
  adjectives.forEach(adj => {
    const lowerAdj = adj.toLowerCase()
    if (positiveWords.some(word => lowerAdj.includes(word))) {
      positiveScore++
    }
    if (negativeWords.some(word => lowerAdj.includes(word))) {
      negativeScore++
    }
  })

  // 分析整体文本情感
  const text = doc.out('text').toLowerCase()
  positiveWords.forEach(word => {
    if (text.includes(word)) positiveScore++
  })
  negativeWords.forEach(word => {
    if (text.includes(word)) negativeScore++
  })

  const polarity = positiveScore - negativeScore
  const confidence = Math.abs(polarity) / Math.max(positiveScore + negativeScore, 1)

  return {
    positive: positiveScore,
    negative: negativeScore,
    polarity,
    confidence
  }
}

/**
 * 分析时态模式
 * @param {Object} doc - compromise 文档对象
 * @returns {Object} 时态分析结果
 */
function analyzeTensePattern(doc) {
  const verbs = doc.verbs()

  // 检查是否有过去时态
  const pastVerbs = verbs.toPastTense().out('array')
  const isPast = pastVerbs.length > 0

  // 检查是否有进行时态
  const progressiveVerbs = verbs.toGerund().out('array')
  const isProgressive = progressiveVerbs.length > 0 || doc.has('#Gerund')

  // 检查是否有完成时态
  const perfectVerbs = verbs.conjugate().map(v => v.PastTense).filter(Boolean)
  const isPerfect = perfectVerbs.length > 0

  return {
    isPast,
    isProgressive,
    isPerfect,
    dominantTense: isPast ? 'past' : isProgressive ? 'progressive' : 'present'
  }
}

/**
 * 分析上下文连贯性
 * @param {string} context - 轨迹描述
 * @returns {number} 连贯性分数
 */
function analyzeContextCoherence(context) {
  let coherenceScore = 0

  // 检查时间逻辑词
  const timeLogicWords = ['然后', '接着', '随后', '之后', '现在', '目前']
  timeLogicWords.forEach(word => {
    if (context.includes(word)) coherenceScore += 2
  })

  // 检查因果关系词
  const causalWords = ['因为', '由于', '所以', '因此', '导致']
  causalWords.forEach(word => {
    if (context.includes(word)) coherenceScore += 3
  })

  // 检查地点连续性
  const locationWords = ['从', '到', '经过', '途径', '抵达']
  locationWords.forEach(word => {
    if (context.includes(word)) coherenceScore += 2
  })

  return Math.min(coherenceScore, 5) // 限制最大加分
}

/**
 * 判断是否为终态状态
 * @param {string} status - 物流状态
 * @returns {boolean} 是否为终态
 */
export function isFinalStatus(status) {
  return [
    LOGISTICS_STATUS.DELIVERED,
    LOGISTICS_STATUS.RETURNED,
    LOGISTICS_STATUS.EXCEPTION
  ].includes(status)
}

/**
 * 获取状态的紧急程度
 * @param {string} status - 物流状态
 * @returns {number} 紧急程度 (1-5, 5最紧急)
 */
export function getStatusUrgency(status) {
  const urgencyMap = {
    [LOGISTICS_STATUS.EXCEPTION]: 5,
    [LOGISTICS_STATUS.FAILED_DELIVERY]: 4,
    [LOGISTICS_STATUS.OUT_FOR_DELIVERY]: 3,
    [LOGISTICS_STATUS.IN_TRANSIT]: 2,
    [LOGISTICS_STATUS.PICKED_UP]: 2,
    [LOGISTICS_STATUS.PENDING_PICKUP]: 3,
    [LOGISTICS_STATUS.DELIVERED]: 1,
    [LOGISTICS_STATUS.RETURNED]: 4
  }

  return urgencyMap[status] || 2
}
