/**
 * 将金额元和分部分分离
 * 例： 123分 --> ['1', '23']
 * @param amt 类型Number，单位分
 * @returns {string[]}
 */
export const splitAmt = amt => {
  const amtN = Number(amt) / 100 // 数字类型（单位元），例：1.23 / 0.03
  return splitAmtFromYuan(amtN)
}

/**
 * 将金额元和分部分分离
 * 例： 1.23元 --> ['1', '23']
 * @param amt 类型Number，单位分
 * @returns {string[]}
 */
export const splitAmtFromYuan = amt => {
  const amtN = Number(amt)// 数字类型（单位元），例：1.23 / 0.03
  const amtS = amtN.toFixed(2) // 字符串类型（单位元）保留2位小数
  return amtS.split('.') // 数组类型，例['1','23'] / ['0','03']
}

/**
 * 分转元
 * @param amt
 * @returns {string}
 */
export const fenToYuan = amt => {
  return (amt / 100).toFixed(2)
}

/**
 * 商品售价和划线价处理
 * @param goods
 * @returns {*[]}
 */
export const priceCompute = goods => {
  const sku = goods && goods.skuList && goods.skuList[0]
    ? goods.skuList[0]
    : {}
  return priceComputeFromSku(sku)
}

/**
 * 商品售价和划线价处理
 * @param sku
 * @returns {*[]}
 */
export const priceComputeFromSku = sku => {
  // 价格数组表，依次是：活动价(如果有)、sku价、划线价(如果有)
  // 后续使用，按顺序获取前两个即可(第一个为展示价、第二个为划线价)
  // 可能的组合：【活动价、sku价】 【sku价、划线价】 【sku价】
  const priceData = []
  const skuPromotionList = sku.skuPromotionList

  // 活动价
  const promotionPrice = (skuPromotionList && skuPromotionList.length > 0) ? skuPromotionList[0].promotionPrice : ''
  if (promotionPrice) priceData.push(promotionPrice)

  // sku价
  const skuPrice = sku.price
  if (skuPrice) priceData.push(skuPrice)

  // 划线价
  const underlinePrice = sku.crossedPrice
  if (underlinePrice) priceData.push(underlinePrice)

  return priceData
}
