<template>
  <div class="refund-application">
    <!-- 基础信息区域 -->
    <section class="refund-basic-info">
      <WoCell left-title="货物状态" :right-title="refundData.goodStatus ? refundData.goodStatus.title : '请选择'"
        :is-center="true" :is-border="true" :is-require="true" :show-arrow="true"
        @right-click="goodsStatusPopupShow = true" />
      <WoCell left-title="退款原因" :right-title="refundData.reasonRefund ? refundData.reasonRefund.title : '请选择'"
        :is-center="true" :is-border="true" :is-require="true" :show-arrow="true"
        @right-click="setReasonRefundPopupShow" />
      <WoCell left-title="退款件数" :is-center="true" :is-border="false">
        <template #right>
          <div class="quantity-stepper">
            <van-stepper @blur="blurRefundsNumber" @minus="minusRefundsNumber" @plus="plusRefundsNumber"
              @change="changeRefundsNumber" integer :disabled="isGoodsNumInput" :max="goodsNum"
              v-model="refundData.refundNum" />
          </div>
        </template>
      </WoCell>
    </section>

    <div class="section-divider"></div>

    <!-- 退款金额区域 -->
    <section class="refund-amount-section">
      <WoCell left-title="退款金额" :is-center="true" :is-border="false" :is-require="true" :is-vertical="true">
        <template #right>
          <div class="amount-input-wrapper">
            <div class="amount-display">
              <span @click="setIsRefundAmountEdit" v-if="!isRefundAmountEdit" :style="inputStyle" class="amount-value">
                {{ getNewSplitMoney }}
              </span>
              <input ref="inputMoneyRef" :style="inputStyle" v-else type="number" class="amount-input"
                v-model.number.trim="refundData.refundAmount" @blur="blurRefundAmount" @input="inputRefundAmount" />
            </div>
            <img @click="setIsRefundAmountEdit" class="edit-icon" src="./assets/edit.png" alt="编辑">
          </div>
          <div class="amount-tips" v-if="refundData.goodStatus && refundData.goodStatus.id === '0'">
            不可修改，商品总金额 ¥{{ goodPrice }} 元
          </div>
          <div class="amount-tips" v-if="refundData.goodStatus && refundData.goodStatus.id === '1'">
            最多可退款 ¥{{ goodPrice }} 元
          </div>
        </template>
      </WoCell>
    </section>

    <div class="section-divider"></div>

    <!-- 补充信息区域 -->
    <section class="refund-supplement-section">
      <!-- 上传凭证卡片 -->
      <div class="info-card">
        <header class="card-header">
          <h3 class="card-title" :class="{ 'required': isRequiredVoucher }">
            上传凭证
          </h3>
        </header>
        <div class="card-body">
          <!-- 空状态上传区域 -->
          <div class="upload-empty-state" v-if="fileList.length < 1">
            <van-uploader ref="uploaderRef" accept="image/*" :after-read="afterRead" :before-read="handleBeforeRead"
              class="primary-uploader" preview-size="0" :deletable="false" v-model="fileList" multiple :max-count="4"
              :preview-image="false">
              <div class="upload-placeholder">
                <img class="upload-icon" src="./assets/相机icon.png" alt="上传">
                <p class="upload-title">上传凭证</p>
                <p class="upload-description">（最多4张，图片大小不能超过10M）</p>
              </div>
            </van-uploader>
            <div class="upload-mask" @click="captureShow = true" v-if="isEnv"></div>
          </div>

          <!-- 已上传图片展示区域 -->
          <div class="uploaded-images" v-if="fileList.length >= 1">
            <!-- 继续上传按钮 -->
            <div class="add-more-upload" v-if="fileList.length <= 3">
              <van-uploader ref="uploaderRef" accept="image/*" :after-read="afterRead" :before-read="handleBeforeRead"
                class="secondary-uploader" preview-size="0" :deletable="false" v-model="fileList" multiple
                :max-count="4" :preview-image="false">
                <img class="upload-icon" src="./assets/相机icon.png" alt="继续上传">
                <p class="upload-counter">（{{ fileList.length }}/4）</p>
              </van-uploader>
              <div class="upload-mask" @click="captureShow = true" v-if="isEnv"></div>
            </div>

            <!-- 图片列表 -->
            <div class="image-item" v-for="item in fileList" :key="item.content">
              <img class="image-preview" :src="item.content" alt="上传的图片" />
              <button @click="deleteUploadImg(item)" class="delete-btn" type="button">
                <img src="./assets/close.png" alt="删除">
              </button>

              <!-- 上传状态遮罩 -->
              <div class="upload-status-overlay" v-if="item.status === 'uploading'">
                <van-loading size="24" />
                <p class="status-text">上传中...</p>
              </div>
              <div class="upload-status-overlay" v-if="item.status === 'failed'">
                <van-icon name="close" size="24" />
                <p class="status-text">上传失败</p>
              </div>
            </div>

            <!-- 占位元素保持布局 -->
            <template v-if="fileList.length <= 2">
              <div class="image-placeholder" v-for="item in (3 - fileList.length)" :key="item + '_placeholder'"></div>
            </template>
          </div>
        </div>
      </div>

      <!-- 补充描述卡片 -->
      <div class="info-card">
        <header class="card-header">
          <h3 class="card-title">补充描述</h3>
        </header>
        <div class="card-body">
          <van-field v-model="refundData.additionalRefundMsg" rows="3" label="" type="textarea" maxlength="200"
            show-word-limit placeholder="补充描述，有助于商家更好地处理售后问题" />
        </div>
      </div>
    </section>

    <!-- 底部操作区域 -->
    <footer class="action-footer">
      <button class="submit-btn" @click="submitApplication" type="button">
        提交申请
      </button>
    </footer>

    <!-- 货物状态选择弹窗 -->
    <van-popup class="selection-popup goods-status-popup" :style="{ minHeight: '100px' }" safe-area-inset-bottom
      lock-scroll round position="bottom" v-model:show="goodsStatusPopupShow">
      <header class="popup-header">
        <h3 class="popup-title">货物状态</h3>
        <button @click="popupClose" class="close-btn" type="button">
          <img src="./assets/popupClose.png" alt="关闭">
        </button>
      </header>
      <div class="popup-body">
        <ul class="option-list">
          <li class="option-item" @click="goodStatusItemSelect(item)" v-for="item in goodStatusEnumList"
            :key="item.id + item.title">
            <span class="option-text">{{ item.title }}</span>
            <img v-if="item.isSelect" class="selection-icon" src="./assets/selectNode.png" alt="已选择">
            <img v-else class="selection-icon" src="./assets/noSelect.png" alt="未选择">
          </li>
        </ul>
      </div>
    </van-popup>

    <!-- 退款原因选择弹窗 -->
    <van-popup class="selection-popup refund-reason-popup" :style="{ minHeight: '100px' }" safe-area-inset-bottom
      lock-scroll round position="bottom" v-model:show="reasonRefundPopupShow">
      <header class="popup-header">
        <h3 class="popup-title">选择退款原因</h3>
        <button @click="popupClose" class="close-btn" type="button">
          <img src="./assets/popupClose.png" alt="关闭">
        </button>
      </header>
      <div class="popup-body">
        <ul class="option-list">
          <li class="option-item" @click="reasonRefundItemSelect(item)" v-for="item in reasonRefundEnumList"
            :key="item.id + item.title">
            <span class="option-text">{{ item.title }}</span>
            <img v-if="item.isSelect" class="selection-icon" src="./assets/selectNode.png" alt="已选择">
            <img v-else class="selection-icon" src="./assets/noSelect.png" alt="未选择">
          </li>
        </ul>
      </div>
    </van-popup>

    <!-- 拍照选择弹窗 -->
    <van-action-sheet @select="captureActionSheetSelect" v-model:show="captureShow" :actions="captureOption"
      cancel-text="取消" description="选择方式" close-on-click-action />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, shallowRef, markRaw } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { isAndroid, isWopay } from 'commonkit'
import { fenToYuan } from '@/utils/amount'
import { applyAfterSaleForRefund } from '@/api/interface/afterSales'
import { useImageCompressor } from '@/hooks/hooks'
import { map, filter, some } from 'lodash-es'
import axios from 'axios'

const router = useRouter()
const route = useRoute()

const reasonRefundEnumList1 = markRaw([
  {
    title: '与商家协商一致退款',
    id: '1',
    isSelect: false
  },
  {
    title: '商品质量不好',
    id: '7',
    isSelect: false
  },
  {
    title: '商品与描述不符',
    id: '8',
    isSelect: false
  },
  {
    title: '商品破损/包装问题',
    id: '9',
    isSelect: false
  },
  {
    title: '少发/漏件',
    id: '6',
    isSelect: false
  },
  {
    title: '商家发错货',
    id: '10',
    isSelect: false
  },
  {
    title: '其他',
    id: '20',
    isSelect: false
  }
])

const reasonRefundEnumList2 = markRaw([
  {
    title: '与商家协商一致退款',
    id: '1',
    isSelect: false
  },
  {
    title: '商品买贵了或降价',
    id: '2',
    isSelect: false
  },
  {
    title: '多拍/拍错/不想要',
    id: '3',
    isSelect: false
  },
  {
    title: '快递一直未送达',
    id: '4',
    isSelect: false
  },
  {
    title: '地址填错/不方便收货',
    id: '5',
    isSelect: false
  },
  {
    title: '少发/漏发',
    id: '6',
    isSelect: false
  },
  {
    title: '其他',
    id: '20',
    isSelect: false
  }
])

// 响应式数据
const goodsStatusPopupShow = ref(false)
const goodStatusEnumList = shallowRef(markRaw([
  {
    title: '已收货',
    id: '1',
    isSelect: false
  },
  {
    title: '未收到货/已拒收',
    id: '0',
    isSelect: false
  }
]))
const reasonRefundPopupShow = ref(false)
const reasonRefundEnumList = shallowRef([])
const refundData = ref({
  goodStatus: '',
  reasonRefund: '',
  refundAmount: 0,
  refundNum: 1,
  additionalRefundMsg: '',
  supplierSubOrderId: ''
})
const goodsNum = ref(1)
const goodPrice = ref(0)
const fileList = shallowRef([])
const isRefundAmountEdit = ref(false)
const capture = ref('')
const captureShow = ref(false)
const captureOption = shallowRef(markRaw([
  { name: '相册', type: 1 },
  { name: '相机', type: 2 }
]))
const inputMoneyRef = ref(null)
const uploaderRef = ref(null)

// 初始化数据
const initPageData = () => {
  const { supplierSubOrderId, orderPrice, skuNum } = route.query
  goodsNum.value = Number(skuNum) || 1
  goodPrice.value = orderPrice ? fenToYuan(orderPrice) : 0
  refundData.value.refundNum = goodsNum.value
  refundData.value.refundAmount = goodPrice.value
  refundData.value.supplierSubOrderId = supplierSubOrderId
}

initPageData()

// 计算属性
const isGoodsNumInput = computed(() => {
  if (!refundData.value.goodStatus) {
    return true
  }
  return refundData.value.goodStatus.id === '0'
})

const isRequiredVoucher = computed(() => {
  const isRequiredList = ['7', '8', '9', '6', '10']
  const goodStatus = refundData.value.goodStatus
  const reasonRefund = refundData.value.reasonRefund

  return goodStatus?.id === '1' && reasonRefund && some(isRequiredList, id => id === reasonRefund.id)
})

const isEnv = computed(() => {
  return isAndroid && isWopay
})

const getNewSplitMoney = computed(() => {
  return refundData.value.refundAmount
})

const inputStyle = computed(() => {
  const length = String(refundData.value.refundAmount).length
  return {
    width: `${Math.max(length * 10 + 20, 30)}px`,
    padding: '0 5px'
  }
})

// 方法
const setReasonRefundPopupShow = () => {
  if (!refundData.value.goodStatus) {
    showToast('请先选择货物状态！')
    return
  }
  reasonRefundPopupShow.value = true
}

const captureActionSheetSelect = (item) => {
  if (item.type === 2) {
    capture.value = 'camera'
    nextTick(() => {
      uploaderRef.value.$refs.input.setAttribute('capture', 'camera')
    })
  } else {
    capture.value = ''
    nextTick(() => {
      uploaderRef.value.$refs.input.removeAttribute('capture')
    })
  }
  nextTick(() => {
    uploaderRef.value.chooseFile()
  })
}

const setIsRefundAmountEdit = () => {
  if (!refundData.value.goodStatus) {
    showToast('请先选择货物状态！')
    return
  }
  if (refundData.value.goodStatus.id === '0') {
    showToast('退款金额不可修改！')
    isRefundAmountEdit.value = false
    return
  }
  isRefundAmountEdit.value = true
  nextTick(() => {
    inputMoneyRef.value.focus()
  })
}

const submitApplication = async () => {
  const { goodStatus, reasonRefund, refundAmount, refundNum, additionalRefundMsg, supplierSubOrderId } = refundData.value

  if (!goodStatus) {
    showToast('请选择货物状态')
    return
  }
  if (!reasonRefund) {
    showToast('请选择退货原因')
    return
  }
  if (isRequiredVoucher.value && fileList.value.length === 0) {
    showToast('请上传凭证')
    return
  }

  const files = fileList.value
  if (files.length > 0) {
    const uploadingFiles = filter(files, { status: 'uploading' })
    if (uploadingFiles.length > 0) {
      showToast('图片上传中，请稍后提交')
      return
    }

    const failedFiles = filter(files, { status: 'failed' })
    if (failedFiles.length > 0) {
      showToast('有图片上传失败，请重新上传')
      return
    }
  }

  const voucherImageUrlList = map(files, 'imgUrl')
  const params = {
    supplierSubOrderId,
    applyRefundMoney: refundAmount * 100,
    additionalRefundMsg,
    isRecived: goodStatus.id,
    refundReasonNum: reasonRefund.id,
    applyRefundSkuNum: refundNum,
    voucherImageUrl: voucherImageUrlList.join(',')
  }

  showLoadingToast()
  try {
    const [err, res] = await applyAfterSaleForRefund({
      AfterSaleRefundOrderVo: JSON.stringify(params)
    })
    closeToast()
    if (!err) {
      router.replace({
        path: '/wo-after-sales-detail',
        query: {
          afterSaleId: res,
          type: 1
        }
      })
    } else {
      showToast(err.msg)
    }
  } catch (error) {
    closeToast()
    showToast('提交失败，请重试')
  }
}

const updateRefundAmount = () => {
  const averagePrice = goodPrice.value / goodsNum.value
  refundData.value.refundAmount = Number((averagePrice * refundData.value.refundNum).toFixed(2))
}

const changeRefundsNumber = () => {
  updateRefundAmount()
}

const minusRefundsNumber = () => {
  updateRefundAmount()
}

const plusRefundsNumber = () => {
  // 原代码为空函数，保持不变
}

const blurRefundsNumber = () => {
  if (refundData.value.refundNum > goodsNum.value) {
    showToast(`当前最多可退${goodsNum.value}件`)
  }
}

const { beforeRead } = useImageCompressor()

const handleBeforeRead = async (file) => {
  return await beforeRead(file, {
    maxSize: 10,
    showLoadingMessage: false,
    showToast,
    showLoadingToast,
    closeToast
  })
}

const afterRead = async (file) => {
  file.status = 'uploading'
  file.message = '上传中...'

  const formData = new FormData()
  formData.append('file', file.file)

  try {
    const res = await axios.post('/ps-ccms-core-front/v2/afterSale/uploadVoucherImageUrl', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    if (res.data.code === '0000') {
      file.status = 'success'
      file.message = '上传成功'
      file.imgUrl = res.data.data.imgUrl
    } else {
      file.status = 'failed'
      file.message = '上传失败'
    }
  } catch (error) {
    file.status = 'failed'
    file.message = '上传失败'
  }
}

const deleteUploadImg = (file) => {
  fileList.value = filter(fileList.value, item => item.content !== file.content)
}

const popupClose = () => {
  reasonRefundPopupShow.value = false
  goodsStatusPopupShow.value = false
}

const inputRefundAmount = (e) => {
  refundData.value.refundAmount = e.target.value
}

const blurRefundAmount = () => {
  const amount = Number(refundData.value.refundAmount)
  const maxAmount = goodPrice.value

  if (!amount || amount <= 0) {
    showToast('退款金额必须大于0元')
    refundData.value.refundAmount = maxAmount
    isRefundAmountEdit.value = false
    return
  }

  if (amount < 0.01) {
    refundData.value.refundAmount = 0.01
  } else if (amount > maxAmount) {
    showToast(`最多退款¥${maxAmount}`)
    refundData.value.refundAmount = maxAmount
  } else {
    refundData.value.refundAmount = Number(amount.toFixed(2))
  }

  isRefundAmountEdit.value = false
}

const goodStatusItemSelect = (item) => {
  goodStatusEnumList.value.forEach(i => {
    i.isSelect = false
  })
  item.isSelect = true
  refundData.value.goodStatus = item
  goodsStatusPopupShow.value = false

  if (item.id === '1') {
    refundData.value.refundAmount = goodPrice.value
    refundData.value.reasonRefund = ''
    reasonRefundEnumList1.forEach(i => {
      i.isSelect = false
    })
    reasonRefundEnumList.value = reasonRefundEnumList1
  } else {
    refundData.value.refundAmount = goodPrice.value
    refundData.value.reasonRefund = ''
    reasonRefundEnumList2.forEach(i => {
      i.isSelect = false
    })
    reasonRefundEnumList.value = reasonRefundEnumList2
  }
}

const reasonRefundItemSelect = (item) => {
  reasonRefundEnumList.value.forEach(i => {
    i.isSelect = false
  })
  item.isSelect = true
  refundData.value.reasonRefund = item
  reasonRefundPopupShow.value = false
}

// 生命周期钩子
onMounted(() => {
  goodsStatusPopupShow.value = true
})
</script>

<style scoped lang="less">
.refund-application {
  width: 100vw;
  padding-bottom: 100px;
  overflow: hidden;

  // 区域分隔线
  .section-divider {
    width: 100%;
    height: 10px;
    background: @bg-color-gray;
  }

  // 基础信息区域
  .refund-basic-info {
    box-sizing: border-box;
    padding: 0 10px;

    .quantity-stepper {
      :deep(.van-stepper__input) {
        margin: 0;
        border-top: 1px solid rgba(232, 232, 232, 1);
        border-bottom: 1px solid rgba(232, 232, 232, 1);
        background-color: @bg-color-white;
      }

      :deep(.van-stepper__minus) {
        color: #000000;
        border: 1px solid rgba(232, 232, 232, 1);
        background-color: @bg-color-white;
        border-radius: 15px 0 0 15px;

        &.van-stepper__minus--disabled {
          color: #c8c9cc;
          background-color: #f7f8fa;
          cursor: not-allowed;
        }
      }

      :deep(.van-stepper__plus) {
        color: #000000;
        border: 1px solid rgba(232, 232, 232, 1);
        background-color: @bg-color-white;
        border-radius: 0 15px 15px 0;

        &.van-stepper__plus--disabled {
          color: #c8c9cc;
          background-color: #f7f8fa;
          cursor: not-allowed;
        }
      }
    }
  }

  // 退款金额区域
  .refund-amount-section {
    box-sizing: border-box;
    padding: 0 10px;

    .amount-input-wrapper {
      width: 100%;
      text-align: right;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .amount-display {
        display: flex;
        align-items: baseline;
        font-size: @font-size-18;
        color: @color-orange;
        font-weight: @font-weight-400;
        height: 22px;
        line-height: 20px;

        .amount-value,
        .amount-input {
          display: flex;
          font-weight: @font-weight-600;
          outline: none;
          border: none;
          background: transparent;
          box-shadow: none;

          &:focus {
            outline: none;
            border: none;
            box-shadow: none;
            transform: none;
          }
        }

        &:before {
          content: '￥';
          font-size: @font-size-13;
        }
      }

      .edit-icon {
        margin-left: 8px;
        width: 14px;
        height: 14px;
        cursor: pointer;
      }
    }

    .amount-tips {
      margin-top: 8px;
      font-size: @font-size-12;
      color: @text-color-tertiary;
      text-align: right;
      line-height: 1;
      font-weight: @font-weight-400;
    }
  }

  // 补充信息区域
  .refund-supplement-section {
    box-sizing: border-box;
    padding: 0 10px;

    .info-card {
      padding: 17px 0 0 0;

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        .card-title {
          font-size: @font-size-16;
          color: @text-color-primary;
          line-height: 1;
          font-weight: @font-weight-400;
          margin: 0;

          &.required {
            position: relative;

            &:after {
              content: '*';
              position: absolute;
              top: -3px;
              right: -10px;
              color: @color-red;
              font-size: 25px;
            }
          }
        }
      }

      .card-body {
        width: 100%;
        min-height: 100px;
        border: 1px solid @divider-color-base;
        border-radius: @radius-10;
        box-sizing: border-box;
        padding: 10px;

        :deep(.van-cell.van-field) {
          padding: 0;
        }

        // 空状态上传区域
        .upload-empty-state {
          width: 100%;
          height: 100%;
          position: relative;

          .primary-uploader {
            width: 100%;
            display: flex;
            justify-content: center;
          }

          .upload-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .upload-icon {
              width: 40px;
              height: 40px;
            }

            .upload-title {
              margin-top: 6px;
              font-size: @font-size-12;
              color: @text-color-tertiary;
              text-align: center;
              line-height: 1;
              font-weight: @font-weight-400;
            }

            .upload-description {
              margin-top: 6px;
              font-size: @font-size-12;
              color: @text-color-tertiary;
              text-align: center;
              line-height: 1;
              font-weight: @font-weight-400;
            }
          }

          .upload-mask {
            position: absolute;
            top: 0;
            left: 0;
            background-color: transparent;
            width: 100%;
            height: 100%;
          }
        }

        // 已上传图片展示区域
        .uploaded-images {
          width: 100%;
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          gap: 2%;

          .add-more-upload {
            position: relative;
            width: 23%;
            height: 75px;

            .secondary-uploader {
              width: 100%;
              height: 75px;
              background: @bg-color-white;
              border: 1px dashed @divider-color-base;
              border-radius: @radius-4;
              padding: 5px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: space-around;
              box-sizing: border-box;

              .upload-icon {
                width: 40px;
                height: 40px;
              }

              .upload-counter {
                font-size: @font-size-12;
                color: @text-color-tertiary;
                text-align: center;
                line-height: 1;
                font-weight: @font-weight-400;
                margin: 0;
              }
            }

            .upload-mask {
              position: absolute;
              top: 0;
              left: 0;
              background-color: transparent;
              width: 100%;
              height: 75px;
            }
          }

          .image-item {
            width: 23%;
            height: 75px;
            background: @bg-color-white;
            border-radius: @radius-4;
            position: relative;

            .image-preview {
              width: 100%;
              height: 75px;
              border-radius: @radius-4;
              object-fit: cover;
            }

            .delete-btn {
              z-index: 55;
              position: absolute;
              top: -7px;
              right: -7px;
              background: none;
              border: none;
              padding: 0;
              cursor: pointer;

              img {
                width: 15px;
                height: 15px;
              }
            }

            .upload-status-overlay {
              z-index: 54;
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              color: @text-color-white;
              background-color: rgba(50, 50, 51, 0.88);
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              border-radius: @radius-4;

              .status-text {
                margin-top: 6px;
                padding: 0 4px;
                font-size: @font-size-12;
                line-height: 14px;
              }
            }
          }

          .image-placeholder {
            width: 23%;
            height: 75px;
          }
        }
      }
    }
  }

  // 底部操作区域
  .action-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    box-sizing: border-box;
    padding: 12px @padding-page;
    width: 100%;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: @bg-color-white;

    .submit-btn {
      width: 100%;
      height: 40px;
      background-image: @gradient-orange-106;
      border-radius: @radius-22;
      text-align: center;
      line-height: 40px;
      font-size: @font-size-16;
      color: @text-color-white;
      font-weight: @font-weight-500;
      border: none;
      cursor: pointer;

      &:active {
        opacity: 0.8;
      }
    }
  }
}

// 选择弹窗样式
.selection-popup {
  box-sizing: border-box;
  padding: 20px;

  .popup-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .popup-title {
      flex: 1;
      font-size: @font-size-17;
      color: @text-color-primary;
      text-align: center;
      line-height: 1;
      font-weight: @font-weight-400;
      margin: 0;
    }

    .close-btn {
      background: none;
      border: none;
      padding: 0;
      cursor: pointer;

      img {
        width: 14px;
        height: 14px;
      }
    }
  }

  .popup-body {
    .option-list {
      list-style: none;
      padding: 0;
      margin: 0;

      .option-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 45px;
        cursor: pointer;

        &:hover {
          background-color: @bg-color-gray;
        }

        .option-text {
          margin-right: 10px;
          font-size: @font-size-15;
          color: @text-color-primary;
          line-height: 1;
          font-weight: @font-weight-400;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .selection-icon {
          width: 18px;
          height: 18px;
          flex-shrink: 0;
        }
      }
    }
  }
}
</style>
