<template>
  <div class="return-goods-page">
    <!-- 退款基本信息 -->
    <section class="refund-basic-info">
      <WoCell left-title="退款原因" :right-title="refundData.reasonRefund ? refundData.reasonRefund.title : '请选择'"
        :is-center="true" :is-border="true" :is-require="true" :show-arrow="true"
        @right-click="setReasonRefundPopupShow" />
      <WoCell left-title="退款件数" :is-center="true" :is-border="false">
        <template #right>
          <div class="quantity-stepper">
            <van-stepper @blur="blurRefundsNumber" @minus="minusRefundsNumber" @plus="plusRefundsNumber"
              @change="changeRefundsNumber" integer :disabled="isGoodsNumInput" :max="goodsNum"
              v-model="refundData.refundNum" />
          </div>
        </template>
      </WoCell>
    </section>

    <div class="section-divider"></div>

    <!-- 退款金额 -->
    <section class="refund-amount-section">
      <WoCell left-title="退款金额" :is-center="true" :is-border="false" :is-require="true" :is-vertical="true">
        <template #right>
          <div class="amount-editor">
            <div class="amount-input">
              <span @click="setIsRefundAmountEdit" v-if="!isRefundAmountEdit" :style="inputStyle"
                class="amount-display">
                {{ getNewSplitMoney }}
              </span>
              <input ref="inputMoneyRef" :style="inputStyle" v-else type="number" class="amount-input-field"
                v-model.number.trim="refundData.refundAmount" @blur="blurRefundAmount" @input="inputRefundAmount" />
            </div>
            <img @click="setIsRefundAmountEdit" class="edit-icon" src="./assets/edit.png" alt="编辑">
          </div>
          <div class="amount-limit-tip">最多可退款 ¥{{ goodPrice }} 元</div>
        </template>
      </WoCell>
    </section>

    <div class="section-divider"></div>

    <!-- 寄付信息 -->
    <section class="shipping-info">
      <WoCell left-title="寄付方式" :is-center="true" :is-border="true">
        <template #right>
          <div class="shipping-method">
            <div class="method-name">自行寄回</div>
            <div class="method-tip">运费自付/垫付</div>
          </div>
        </template>
      </WoCell>
      <WoCell left-title="寄件地址" :is-center="true" :is-border="false" :show-arrow="true" @right-click="chooseAddress">
        <template #right>
          <div class="address-info">
            <div class="address-detail">{{ locationText }}</div>
            <div class="contact-info" v-if="locationUserText">{{ locationUserText }}</div>
          </div>
        </template>
      </WoCell>
    </section>

    <div class="section-divider"></div>

    <!-- 补充信息 -->
    <section class="supplement-section">
      <!-- 上传凭证 -->
      <div class="info-card">
        <header class="card-header">
          <h3 class="card-title" :class="{ 'required': isRequiredVoucher }">
            上传凭证
          </h3>
        </header>
        <div class="card-content">
          <!-- 空状态上传区域 -->
          <div class="upload-area-empty" v-if="fileList.length < 1">
            <van-uploader ref="uploaderRef" accept="image/*" :capture="capture" :after-read="afterRead"
              :before-read="handleBeforeRead" class="primary-uploader" preview-size="0" :deletable="false"
              v-model="fileList" multiple :max-count="4" :preview-image="false">
              <div class="upload-placeholder">
                <img class="upload-icon" src="./assets/相机icon.png" alt="上传">
                <p class="upload-title">上传凭证</p>
                <p class="upload-hint">（最多4张，图片大小不能超过10M）</p>
              </div>
            </van-uploader>
            <div class="upload-mask" @click="captureShow = true" v-if="isEnv"></div>
          </div>

          <!-- 已有图片的上传区域 -->
          <div class="upload-area-filled" v-if="fileList.length >= 1">
            <!-- 添加更多图片按钮 -->
            <div class="add-more-uploader" v-if="fileList.length <= 3">
              <van-uploader ref="uploaderRef" accept="image/*" :after-read="afterRead" :before-read="handleBeforeRead"
                class="secondary-uploader" preview-size="0" :deletable="false" v-model="fileList" multiple
                :max-count="4" :preview-image="false">
                <img class="upload-icon" src="./assets/相机icon.png" alt="添加">
                <p class="upload-counter">（{{ fileList.length }}/4）</p>
              </van-uploader>
              <div class="upload-mask" @click="captureShow = true" v-if="isEnv"></div>
            </div>

            <!-- 图片列表 -->
            <div class="image-item" v-for="item in fileList" :key="item.content">
              <img class="preview-image" :src="item.content" alt="预览图" />
              <button @click="deleteUploadImg(item)" class="delete-btn" type="button" aria-label="删除图片">
                <img src="./assets/close.png" alt="删除">
              </button>

              <!-- 上传状态遮罩 -->
              <div class="upload-status-overlay" v-if="item.status === 'uploading'">
                <van-loading size="24" />
                <p class="status-text">上传中...</p>
              </div>
              <div class="upload-status-overlay" v-if="item.status === 'failed'">
                <van-icon name="close" size="24" />
                <p class="status-text">上传失败</p>
              </div>
            </div>

            <!-- 占位元素保持布局 -->
            <template v-if="fileList.length <= 2">
              <div class="image-placeholder" v-for="item in (3 - fileList.length)" :key="item + '_placeholder'"></div>
            </template>
          </div>
        </div>
      </div>

      <!-- 补充描述 -->
      <div class="info-card">
        <header class="card-header">
          <h3 class="card-title">补充描述</h3>
        </header>
        <div class="card-content">
          <van-field v-model="refundData.additionalReturnMsg" rows="3" label="" type="textarea" maxlength="200"
            show-word-limit placeholder="补充描述，有助于商家更好地处理售后问题" />
        </div>
      </div>
    </section>

    <!-- 底部操作按钮 -->
    <footer class="action-footer">
      <button class="submit-btn" @click="submitApplication" type="button">
        提交申请
      </button>
    </footer>

    <!-- 退款原因选择弹窗 -->
    <van-popup class="reason-selection-popup" :style="{ minHeight: '100px' }" safe-area-inset-bottom lock-scroll round
      position="bottom" v-model:show="reasonRefundPopupShow">
      <header class="popup-header">
        <h2 class="popup-title">选择退款原因</h2>
        <button @click="popupClose" class="close-btn" type="button" aria-label="关闭弹窗">
          <img src="./assets/popupClose.png" alt="关闭">
        </button>
      </header>
      <div class="popup-content">
        <ul class="reason-list">
          <li class="reason-item" @click="reasonRefundItemSelect(item)" v-for="item in reasonRefundEnumList"
            :key="item.id + item.title">
            <span class="reason-text">{{ item.title }}</span>
            <img v-if="item.isSelect" class="selection-icon" src="./assets/selectNode.png" alt="已选择">
            <img v-else class="selection-icon" src="./assets/noSelect.png" alt="未选择">
          </li>
        </ul>
      </div>
    </van-popup>

    <!-- 拍照方式选择 -->
    <van-action-sheet @select="captureActionSheetSelect" v-model:show="captureShow" :actions="captureOption"
      cancel-text="取消" description="选择方式" close-on-click-action />

    <!-- 地址选择弹窗 -->
    <PopupAddrList v-model:show="showLocationSelector" :simpleAddMode="false" :isShowLogistics="false"
      @change="onPopupAddrChange" />
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, shallowRef, markRaw } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { axios, isAndroid, isWopay } from 'commonkit'
import { applyAfterSaleForReturn } from '@/api/interface/afterSales'
import { fenToYuan } from '@/utils/amount'
import PopupAddrList from '@/components/Address/AddressQuickSelectionPopup.vue'
import { useImageCompressor } from '@/hooks/hooks'
import { useUserStore } from '@/store/modules/user'
import WoCell from '@components/WoElementCom/WoCell/WoCell.vue'
import { map, filter, some } from 'lodash-es'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 引用
const uploaderRef = ref(null)
const inputMoneyRef = ref(null)

// 数据
const reasonRefundPopupShow = ref(false)
const reasonRefundEnumList = shallowRef(markRaw([
  {
    title: '多拍/拍错/不想要',
    id: '1',
    isSelect: false
  },
  {
    title: '商品买贵了或降价',
    id: '2',
    isSelect: false
  },
  {
    title: '商品质量不好',
    id: '3',
    isSelect: false
  },
  {
    title: '商品与描述不符',
    id: '4',
    isSelect: false
  },
  {
    title: '商品破损/包装问题',
    id: '5',
    isSelect: false
  },
  {
    title: '少发/漏发',
    id: '6',
    isSelect: false
  },
  {
    title: '商家发错货',
    id: '7',
    isSelect: false
  },
  {
    title: '其他',
    id: '8',
    isSelect: false
  }
]))

const refundData = ref({
  reasonRefund: '',
  refundAmount: 0,
  refundNum: 0,
  additionalReturnMsg: '',
  supplierSubOrderId: ''
})

const goodsNum = ref(1)
const goodPrice = ref(0)
const fileList = shallowRef([])
const isRefundAmountEdit = ref(false)
const capture = ref('')
const captureShow = ref(false)
const captureOption = shallowRef(markRaw([
  { name: '相册', type: 1 },
  { name: '相机', type: 2 }
]))
const showLocationSelector = ref(false)

// 初始化数据
const initData = () => {
  const { supplierSubOrderId, orderPrice, skuNum } = route.query
  goodsNum.value = Number(skuNum) || 1
  goodPrice.value = orderPrice ? fenToYuan(orderPrice) : 0
  refundData.value.refundNum = goodsNum.value
  refundData.value.refundAmount = goodPrice.value
  refundData.value.supplierSubOrderId = supplierSubOrderId
}

// 计算属性
const isGoodsNumInput = computed(() => {
  return false
})

const isRequiredVoucher = computed(() => {
  const isRequiredList = ['3', '4', '5', '6', '7']
  const reasonRefund = refundData.value.reasonRefund
  return reasonRefund && some(isRequiredList, id => id === reasonRefund.id)
})

const isEnv = computed(() => {
  return isAndroid && isWopay
})

const getNewSplitMoney = computed(() => {
  return refundData.value.refundAmount
})

const inputStyle = computed(() => {
  const length = String(refundData.value.refundAmount).length
  return {
    width: `${Math.max(length * 10 + 20, 30)}px`,
    padding: '0 5px'
  }
})

const curAddrInfo = computed(() => userStore.curAddressInfo)

// 地址回旋
const locationText = computed(() => {
  const info = curAddrInfo.value
  if (!info) return ''
  const { provinceName = '', cityName = '', countyName = '', townName = '', addrDetail = '' } = info
  return `${provinceName} ${cityName} ${countyName} ${townName} ${addrDetail}`.trim()
})

// 地址用户
const locationUserText = computed(() => {
  const info = curAddrInfo.value
  if (!info?.recName && !info?.recPhone) return ''
  return `${info.recName || ''} ${info.recPhone || ''}`.trim()
})

// 方法
const chooseAddress = () => {
  showLocationSelector.value = true
}

const onPopupAddrChange = async () => {
  await userStore.queryDefaultAddr()
}

const setReasonRefundPopupShow = () => {
  reasonRefundPopupShow.value = true
}

const captureActionSheetSelect = (item) => {
  if (item.type === 2) {
    capture.value = 'camera'
    nextTick(() => {
      uploaderRef.value.$refs.input.setAttribute('capture', 'camera')
    })
  } else {
    capture.value = ''
    nextTick(() => {
      uploaderRef.value.$refs.input.removeAttribute('capture')
    })
  }
  nextTick(() => {
    uploaderRef.value.chooseFile()
  })
}

const setIsRefundAmountEdit = () => {
  isRefundAmountEdit.value = true
  nextTick(() => {
    inputMoneyRef.value.focus()
  })
}

const submitApplication = async () => {
  const { reasonRefund, refundAmount, refundNum, additionalReturnMsg, supplierSubOrderId } = refundData.value

  if (!reasonRefund) {
    showToast('请选择退货原因')
    return
  }
  if (isRequiredVoucher.value && fileList.value.length === 0) {
    showToast('请上传凭证')
    return
  }

  const files = fileList.value
  if (files.length > 0) {
    const uploadingFiles = filter(files, { status: 'uploading' })
    if (uploadingFiles.length > 0) {
      showToast('图片上传中，请稍后提交')
      return
    }

    const failedFiles = filter(files, { status: 'failed' })
    if (failedFiles.length > 0) {
      showToast('有图片上传失败，请重新上传')
      return
    }
  }

  const voucherImageUrlList = map(files, 'imgUrl')
  const info = curAddrInfo.value
  const shippingAddress = {
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName,
    addrDetail: info.addrDetail,
    recName: info.recName,
    recPhone: info.recPhone
  }

  const params = {
    supplierSubOrderId,
    applyRefundMoney: refundAmount * 100,
    additionalReturnMsg,
    rejectReasonNum: reasonRefund.id,
    applyRejectSkuNum: refundNum,
    deliverMethod: '1',
    voucherImageUrl: voucherImageUrlList.join(','),
    isRecived: '1',
    shippingAddress
  }

  showLoadingToast({
    message: '提交中...',
    forbidClick: true
  })

  try {
    const res = await applyAfterSaleForReturn({
      AfterSaleReturnOrderVo: JSON.stringify(params)
    })
    closeToast()
    router.replace({
      path: '/wo-after-sales-detail',
      query: {
        afterSaleId: res,
        type: 2
      }
    })
  } catch (err) {
    closeToast()
    showToast(err.msg || '提交失败，请重试')
  }
}

const updateRefundAmount = () => {
  const averagePrice = goodPrice.value / goodsNum.value
  refundData.value.refundAmount = Number((averagePrice * refundData.value.refundNum).toFixed(2))
}

const changeRefundsNumber = () => {
  updateRefundAmount()
}

const minusRefundsNumber = () => {
  updateRefundAmount()
}

const plusRefundsNumber = () => {
  // 保留原有空方法
}

const blurRefundsNumber = () => {
  if (refundData.value.refundNum > goodsNum.value) {
    showToast(`当前最多可退${goodsNum.value}件`)
  }
}

const { beforeRead } = useImageCompressor()

const handleBeforeRead = async (file) => {
  return await beforeRead(file, {
    maxSize: 10,
    showLoadingMessage: true,
    loadingMessage: '处理中...',
    showToast,
    showLoadingToast,
    closeToast
  })
}

const afterRead = async (file) => {
  file.status = 'uploading'
  file.message = '上传中...'

  const formData = new FormData()
  formData.append('file', file.file)

  try {
    const res = await axios.post('/ps-ccms-core-front/v2/afterSale/uploadVoucherImageUrl', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    if (res.data.code === '0000') {
      file.status = 'success'
      file.message = '上传成功'
      file.imgUrl = res.data.data.imgUrl
    } else {
      file.status = 'failed'
      file.message = '上传失败'
    }
  } catch (error) {
    file.status = 'failed'
    file.message = '上传失败'
  }
}

const deleteUploadImg = (file) => {
  fileList.value = filter(fileList.value, item => item.content !== file.content)
}

const popupClose = () => {
  reasonRefundPopupShow.value = false
}

const inputRefundAmount = (e) => {
  refundData.value.refundAmount = e.target.value
}

const blurRefundAmount = () => {
  const amount = Number(refundData.value.refundAmount)
  const maxAmount = goodPrice.value

  if (!amount || amount <= 0) {
    showToast('退款金额必须大于0元')
    refundData.value.refundAmount = maxAmount
    isRefundAmountEdit.value = false
    return
  }

  if (amount < 0.01) {
    refundData.value.refundAmount = 0.01
  } else if (amount > maxAmount) {
    showToast(`最多退款¥${maxAmount}`)
    refundData.value.refundAmount = maxAmount
  } else {
    refundData.value.refundAmount = Number(amount.toFixed(2))
  }

  isRefundAmountEdit.value = false
}

const reasonRefundItemSelect = (item) => {
  reasonRefundEnumList.value.forEach(i => {
    i.isSelect = false
  })
  item.isSelect = true
  refundData.value.reasonRefund = item
  reasonRefundPopupShow.value = false
}

// 生命周期钩子
onMounted(() => {
  initData()
  reasonRefundPopupShow.value = true
})
</script>

<style scoped lang="less">
.return-goods-page {
  width: 100vw;
  padding-bottom: 100px;
  overflow: hidden;

  // 分割线
  .section-divider {
    width: 100%;
    height: @padding-page * 2;
    background: rgba(247, 247, 247, 0.80);
  }

  // 基础信息区域
  .refund-basic-info,
  .refund-amount-section,
  .shipping-info {
    box-sizing: border-box;
    padding: 0 10px;

    // 数量步进器
    .quantity-stepper {
      :deep(.van-stepper__input) {
        margin: 0;
        border-top: 1px solid rgba(232, 232, 232, 1);
        border-bottom: 1px solid rgba(232, 232, 232, 1);
        background-color: @bg-color-white;
      }

      :deep(.van-stepper__minus) {
        color: #000000;
        border: 1px solid rgba(232, 232, 232, 1);
        background-color: @bg-color-white;
        border-radius: 15px 0 0 15px;

        &.van-stepper__minus--disabled {
          color: #c8c9cc;
          background-color: #f7f8fa;
          cursor: not-allowed;
        }
      }

      :deep(.van-stepper__plus) {
        color: #000000;
        border: 1px solid rgba(232, 232, 232, 1);
        background-color: @bg-color-white;
        border-radius: 0 15px 15px 0;

        &.van-stepper__plus--disabled {
          color: #c8c9cc;
          background-color: #f7f8fa;
          cursor: not-allowed;
        }
      }
    }

    // 金额编辑器
    .amount-editor {
      width: 100%;
      text-align: right;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .amount-input {
        display: flex;
        align-items: baseline;
        font-size: @font-size-18;
        color: @color-orange;
        font-weight: @font-weight-400;
        height: 22px;
        line-height: 20px;

        .amount-display,
        .amount-input-field {
          display: flex;
          font-weight: @font-weight-600;
          outline: none;
          border: none;
          background: transparent;
          box-shadow: none;

          &:focus {
            outline: none;
            border: none;
            box-shadow: none;
            transform: none;
          }
        }

        &:before {
          content: '￥';
          font-size: @font-size-13;
        }
      }

      .edit-icon {
        margin-left: 8px;
        width: 14px;
        height: 14px;
        cursor: pointer;
      }
    }

    .amount-limit-tip {
      margin-top: 8px;
      font-size: @font-size-12;
      color: @text-color-tertiary;
      text-align: right;
      line-height: 1;
      font-weight: @font-weight-400;
    }

    // 寄付方式和地址信息
    .shipping-method,
    .address-info {

      .method-name,
      .address-detail {
        font-size: @font-size-15;
        color: @text-color-primary;
        text-align: right;
        font-weight: @font-weight-400;
        .multi-ellipsis(2);
      }

      .method-tip,
      .contact-info {
        margin-top: 8px;
        font-size: @font-size-12;
        color: @text-color-tertiary;
        text-align: right;
        line-height: 1.2;
        font-weight: @font-weight-400;
      }
    }
  }

  // 补充信息区域
  .supplement-section {
    box-sizing: border-box;
    padding: 0 10px;

    .info-card {
      padding: 17px 0 0 0;

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .card-title {
          font-size: @font-size-16;
          color: @text-color-primary;
          line-height: 1;
          font-weight: @font-weight-400;
          margin: 0 0 10px 0;

          &.required {
            position: relative;

            &:after {
              content: '*';
              position: absolute;
              top: -3px;
              right: -10px;
              color: @color-red;
              font-size: 25px;
            }
          }
        }
      }

      .card-content {
        width: 100%;
        min-height: 100px;
        border: 1px solid @divider-color-base;
        border-radius: @radius-10;
        box-sizing: border-box;
        padding: 10px;

        :deep(.van-cell.van-field) {
          padding: 0;
        }

        // 空状态上传区域
        .upload-area-empty {
          width: 100%;
          height: 100%;
          position: relative;

          .primary-uploader {
            width: 100%;
            display: flex;
            justify-content: center;
          }

          .upload-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .upload-icon {
              width: 40px;
              height: 40px;
            }

            .upload-title {
              margin-top: 6px;
              font-size: @font-size-12;
              color: @text-color-tertiary;
              text-align: center;
              line-height: 1;
              font-weight: @font-weight-400;
            }

            .upload-hint {
              margin-top: 6px;
              font-size: @font-size-12;
              color: @text-color-tertiary;
              text-align: center;
              line-height: 1;
              font-weight: @font-weight-400;
            }
          }

          .upload-mask {
            position: absolute;
            top: 0;
            left: 0;
            background-color: transparent;
            width: 100%;
            height: 100%;
          }
        }

        // 已有图片的上传区域
        .upload-area-filled {
          width: 100%;
          display: flex;
          justify-content: space-between;

          .add-more-uploader {
            position: relative;
            width: 23%;
            height: 100%;

            .secondary-uploader {
              width: 100%;
              height: 75px;
              background: @bg-color-white;
              border: 1px dashed @divider-color-base;
              border-radius: @radius-4;
              padding: 5px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: space-around;
              box-sizing: border-box;

              .upload-icon {
                width: 40px;
                height: 40px;
              }

              .upload-counter {
                font-size: @font-size-12;
                color: @text-color-tertiary;
                text-align: center;
                line-height: 1;
                font-weight: @font-weight-400;
              }
            }

            .upload-mask {
              position: absolute;
              top: 0;
              left: 0;
              background-color: transparent;
              width: 100%;
              height: 75px;
            }
          }

          .image-item {
            width: 23%;
            height: 75px;
            background: @bg-color-white;
            border-radius: @radius-4;
            position: relative;

            .preview-image {
              width: 100%;
              height: 75px;
              border-radius: @radius-4;
              object-fit: cover;
            }

            .delete-btn {
              z-index: 55;
              position: absolute;
              top: -7px;
              right: -7px;
              background: none;
              border: none;
              padding: 0;
              cursor: pointer;

              img {
                width: 15px;
                height: 15px;
              }
            }

            .upload-status-overlay {
              z-index: 54;
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              color: @text-color-white;
              background-color: rgba(50, 50, 51, 0.88);
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              border-radius: @radius-4;

              .status-text {
                margin-top: 6px;
                padding: 0 4px;
                font-size: @font-size-12;
                line-height: 14px;
              }
            }
          }

          .image-placeholder {
            width: 23%;
            height: 75px;
          }
        }
      }
    }
  }

  // 底部操作区域
  .action-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    box-sizing: border-box;
    padding: 12px 17px;
    width: 100%;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: @bg-color-white;

    .submit-btn {
      width: 100%;
      height: @button-height-38;
      background-image: @gradient-orange-106;
      border-radius: @radius-9999;
      border: none;
      text-align: center;
      line-height: @button-height-38;
      font-size: @font-size-16;
      color: @text-color-white;
      font-weight: @font-weight-500;
      cursor: pointer;

      &:active {
        opacity: 0.8;
      }
    }
  }
}

// 弹窗样式
.reason-selection-popup {
  box-sizing: border-box;
  padding: 20px;

  .popup-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .popup-title {
      flex: 1;
      font-size: @font-size-17;
      color: @text-color-primary;
      text-align: center;
      line-height: 1;
      font-weight: @font-weight-400;
      margin: 0;
    }

    .close-btn {
      background: none;
      border: none;
      padding: 0;
      cursor: pointer;

      img {
        width: 14px;
        height: 14px;
      }
    }
  }

  .popup-content {
    .reason-list {
      list-style: none;
      padding: 0;
      margin: 0;

      .reason-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 45px;
        cursor: pointer;

        &:active {
          background-color: rgba(0, 0, 0, 0.05);
        }

        .reason-text {
          margin-right: 10px;
          font-size: @font-size-15;
          color: @text-color-primary;
          line-height: 1;
          font-weight: @font-weight-400;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .selection-icon {
          width: 18px;
          height: 18px;
          flex-shrink: 0;
        }
      }
    }
  }
}
</style>
