<template>
  <aside class="category-sidebar">
    <!-- 骨架屏加载状态 -->
    <CategorySkeleton v-if="isLoading" type="sidebar" :count="8" />

    <!-- 侧边栏内容 -->
    <van-sidebar v-else v-model="localActiveIndex" @change="handleChange" ref="sidebarRef">
      <van-sidebar-item v-for="category in categories" :key="category.id" :title="category.name"
        ref="sidebarItemRefs" />
    </van-sidebar>
  </aside>
</template>

<script setup>
import { ref, toRefs, watch } from 'vue'
import CategorySkeleton from './CategorySkeleton.vue'

const props = defineProps({
  categories: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  activeIndex: {
    default: 0
  }
})

const { categories, isLoading, activeIndex: propActiveIndex } = toRefs(props)

const emit = defineEmits(['change'])

const localActiveIndex  = ref(propActiveIndex.value)
const sidebarRef = ref(null)
const sidebarItemRefs = ref([])

// 监听外部传入的 activeIndex 变化
watch(propActiveIndex, (newIndex) => {
  localActiveIndex.value = newIndex
}, { immediate: true })

const handleChange = (index) => {
  localActiveIndex.value = index
  emit('change', index, categories.value[index])
}

const scrollToCategory = (index) => {
  // 使用 setTimeout 确保 DOM 完全渲染完成
  setTimeout(() => {
    if (!sidebarRef.value || !sidebarItemRefs.value[index]) return

    const sidebarEl = sidebarRef.value.$el
    const selectedItem = sidebarItemRefs.value[index]?.$el

    if (sidebarEl && selectedItem) {
      const itemTop = selectedItem.offsetTop
      const sidebarHeight = sidebarEl.clientHeight
      const itemHeight = selectedItem.clientHeight

      sidebarEl.scrollTo({
        top: itemTop - (sidebarHeight / 2) + (itemHeight / 2),
        behavior: 'smooth'
      })
    }
  }, 100)
}

const setActiveCategory = (index) => {
  if (index >= 0 && index < categories.value.length) {
    localActiveIndex.value = index
    scrollToCategory(index)
  }
}

defineExpose({
  setActiveCategory,
  scrollToCategory
})
</script>

<style scoped lang="less">
.category-sidebar {
  width: 110px;
  flex-shrink: 0;

  :deep(.van-sidebar) {
    width: 110px;
    height: 100%;
    overflow-y: auto;
    background-color: @bg-color-gray;
    text-align: center;
    .no-scrollbar();
  }

  :deep(.van-sidebar-item) {
    padding: 12px 6px;
    font-size: @font-size-13;
    color: @text-color-primary;
    transition: all 0.2s ease;

    &--select {
      color: @theme-color;
      font-weight: @font-weight-500;
      border-color: @theme-color;

      &::before {
        background-color: @theme-color;
      }
    }
  }
}
</style>
