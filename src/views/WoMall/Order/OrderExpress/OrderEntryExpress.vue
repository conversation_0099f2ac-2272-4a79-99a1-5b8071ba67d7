<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showLoadingToast, closeToast } from 'vant'
import { isEmpty, debounce } from 'lodash-es'
import { getOrderExpress } from '@/api/interface/order'

const route = useRoute()
const router = useRouter()

const orderId = ref('')
const orderExpress = ref({})
const isNavigating = ref(false)

// 恢复原来的获取订单快递信息逻辑
const getOrderExpressData = async () => {
  showLoadingToast()
  try {
    const [err, json] = await getOrderExpress(orderId.value)
    closeToast()
    if (!err && !isEmpty(json)) {
      return json
    }
    return {}
  } catch (error) {
    closeToast()
    return {}
  }
}

// 使用计算属性缓存复杂计算
const expressInfo = computed(() => {
  const data = orderExpress.value
  if (isEmpty(data)) return { delivered: [], notDelivered: [], count: 0 }
  
  const delivered = data.orderPackageList || []
  const notDelivered = data.notDelivered || []
  const count = delivered.length + notDelivered.length
  
  return { delivered, notDelivered, count }
})

// 优化的路由跳转逻辑
const gotoExpress = () => {
  if (isNavigating.value) return
  
  const { delivered, count } = expressInfo.value
  
  if (count === 0) {
    return
  }
  
  isNavigating.value = true
  
  try {
    if (count > 1) {
      // 多个快递
      router.replace({
        name: 'user-order-muti-express',
        query: { orderId: orderId.value },
        params: { orderExpress: orderExpress.value }
      })
    } else {
      // 单个快递
      const firstPackage = delivered[0]
      if (!firstPackage?.deliverInfo) {
        return
      }
      
      router.replace({
        name: 'user-order-express',
        query: {
          orderId: orderId.value,
          supplierSubOrderId: firstPackage.deliverInfo.supplierSubOrderId,
          expressNo: firstPackage.deliverInfo.expressNo
        }
      })
    }
  } catch (error) {
  } finally {
    isNavigating.value = false
  }
}

// 使用防抖优化导航函数
const debouncedGotoExpress = debounce(gotoExpress, 100)

onMounted(async () => {
  orderId.value = route.query.orderId
  
  if (!orderId.value) {
    return
  }
  
  const routeOrderExpress = route.params.orderExpress
  
  // 优先使用路由参数中的数据，避免不必要的API调用
  if (routeOrderExpress && !isEmpty(routeOrderExpress) && routeOrderExpress.orderPackageList) {
    orderExpress.value = routeOrderExpress
  } else {
    // 使用原来的数据获取逻辑
    orderExpress.value = await getOrderExpressData()
  }
  
  debouncedGotoExpress()
})

onUnmounted(() => {
  closeToast()
  debouncedGotoExpress.cancel()
})
</script>