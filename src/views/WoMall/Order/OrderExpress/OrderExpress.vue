<template>
  <div class="express-page">
    <!-- 订单信息头部 -->
    <section class="express-page__header">
      <dl class="express-page__info-list">
        <div class="express-page__info-item">
          <dt class="express-page__info-label">订单号：</dt>
          <dd class="express-page__info-value">{{ orderId }}</dd>
        </div>
        <div class="express-page__info-item">
          <dt class="express-page__info-label">承运商：</dt>
          <dd class="express-page__info-value">{{ displayData.expressName }}</dd>
        </div>
        <div class="express-page__info-item">
          <dt class="express-page__info-label">运单号：</dt>
          <dd class="express-page__info-value">
            <span class="express-page__tracking-number">{{ displayData.expressNo }}</span>
            <button v-if="shouldShowCopyButton" class="express-page__copy-button" @click="copyExpressNo"
              aria-label="复制运单号">
              复制
            </button>
          </dd>
        </div>
      </dl>
    </section>

    <!-- 物流跟踪信息 -->
    <section class="express-page__tracking-content" v-if="orderTrackStatus === 1">
      <ol class="express-page__tracking-timeline">
        <li class="express-page__tracking-item" v-for="(item, index) in orderTrack"
          :key="`track-${index}-${item.msgTime || item.time}`">
          <div class="express-page__tracking-text">{{ item.content || item.context }}</div>
          <time class="express-page__tracking-time">{{ item.msgTime || item.time }}</time>
        </li>
      </ol>
    </section>

    <!-- 查询提示 -->
    <section class="express-page__tracking-content express-page__tracking-content--no-data"
      v-else-if="orderTrackStatus === 2">
      <div class="express-page__query-guide">
        <p class="express-page__guide-text">
          您可通过复制物流单号，前往物流公司官网查询物流情况，也可快速访问"快递100"进行查询。快递100：
          <a href="https://www.kuaidi100.com" target="_blank" rel="noopener"
            class="express-page__external-link">https://www.kuaidi100.com</a>
        </p>
        <p class="express-page__guide-note">(建议前往官方网站查询，当查询失效时，可检查单号是否填写正确)</p>
      </div>
    </section>

    <!-- 空状态 -->
    <section class="express-page__tracking-content express-page__tracking-content--empty"
      v-else-if="orderTrackStatus === 0">
      <div class="express-page__empty-state">
        <div class="express-page__empty-icon" role="img" aria-label="暂无数据"></div>
        <p class="express-page__empty-text">暂无消息~</p>
      </div>
    </section>

    <!-- 加载状态 -->
    <section class="express-page__tracking-content" v-else>
      <ol class="express-page__tracking-timeline">
        <li class="express-page__tracking-item express-page__tracking-item--skeleton" v-for="n in 3" :key="n">
          <div class="express-page__skeleton express-page__skeleton--text"></div>
          <div class="express-page__skeleton express-page__skeleton--time"></div>
        </li>
      </ol>
    </section>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { showLoadingToast, closeToast, showToast } from 'vant'
import { getExpress } from '@/api/interface/order'
import useClipboard from 'vue-clipboard3'
import { isEmpty, get, defaults } from 'lodash-es'

// 响应式数据
const orderId = ref('')
const expressName = ref('--')
const expressNo = ref('--')
const orderTrack = ref([])
const orderTrackStatus = ref(-1) // 1-有快递记录，2-有快递但无记录，0-无快递信息（其他情况）,-1-数据读取中
const isLoading = ref(true)

const route = useRoute()
const { toClipboard } = useClipboard()

const shouldShowCopyButton = computed(() =>
  expressNo.value && expressNo.value !== '--'
)

const routeParams = computed(() =>
  defaults(route.query, {
    orderId: '',
    supplierSubOrderId: '',
    expressNo: ''
  })
)

const displayData = computed(() => ({
  expressName: expressName.value || '--',
  expressNo: expressNo.value || '--'
}))

const initializeBasicInfo = () => {
  orderId.value = get(routeParams.value, 'orderId', '')
}

const getExpressData = async (orderIdParam, expressNoParam) => {
  try {
    const [err, json] = await getExpress(orderIdParam, expressNoParam)
    return err ? {} : json
  } catch (error) {
    console.error('获取快递信息失败:', error)
    return {}
  }
}

const updateExpressInfo = (deliverInfo) => {
  const safeDeliverInfo = defaults(deliverInfo, {
    expressName: '--',
    expressNo: '--',
    orderTrack: []
  })

  // 批量更新
  expressName.value = safeDeliverInfo.expressName
  expressNo.value = safeDeliverInfo.expressNo
  orderTrack.value = safeDeliverInfo.orderTrack

  // 确定状态
  if (!isEmpty(deliverInfo) && safeDeliverInfo?.orderTrack?.length > 0) {
    orderTrackStatus.value = 1
  } else if (!isEmpty(deliverInfo) && (safeDeliverInfo.expressName !== '--' || safeDeliverInfo.expressNo !== '--')) {
    orderTrackStatus.value = 2
  } else {
    orderTrackStatus.value = 0
  }
}

const copyExpressNo = async () => {
  try {
    await toClipboard(expressNo.value)
    showToast('复制成功')
  } catch (e) {
    showToast('复制失败')
  }
}

// 组件挂载时执行
onMounted(async () => {
  // 立即初始化基础信息
  initializeBasicInfo()

  // 等待下一个tick确保DOM更新
  await nextTick()

  try {
    showLoadingToast()
    const { supplierSubOrderId, expressNo: expressNoParam } = routeParams.value

    const deliverInfo = await getExpressData(supplierSubOrderId, expressNoParam)
    updateExpressInfo(deliverInfo)
  } catch (error) {
    console.error('加载数据失败:', error)
    orderTrackStatus.value = 0
  } finally {
    isLoading.value = false
    closeToast()
  }
})

// 组件卸载时清理
onUnmounted(() => {
  closeToast()
})
</script>

<style lang="less" scoped>
.express-page {
  width: 100%;
  min-height: 100vh;
  background: @bg-color-white;
  will-change: transform;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &__header {
    contain: layout style;
    padding: 10px;
    width: 100%;
    margin-bottom: 10px;
    line-height: 15px;
    border-bottom: 9px solid @bg-color-gray;
    font-size: @font-size-13;
    color: @text-color-primary;
    box-sizing: border-box;
  }

  &__info-list {
    margin: 0;
  }

  &__info-item {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
    line-height: 1.5;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__info-label {
    margin: 0;
    color: @text-color-primary;
    font-weight: normal;
  }

  &__info-value {
    margin: 0;
    color: @text-color-primary;
    display: flex;
    align-items: center;
  }

  &__tracking-number {
    margin-right: 10px;
  }

  &__copy-button {
    margin-left: 10px;
    padding: 0;
    width: 48px;
    height: 23px;
    line-height: 23px;
    border: 1px solid @color-orange;
    border-radius: @radius-2;
    background: transparent;
    font-size: @font-size-13;
    font-style: normal;
    text-align: center;
    color: @color-orange;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  &__tracking-content {
    padding: 15px 15px 15px 56px;
    width: 100%;
    background: @bg-color-white;
    box-sizing: border-box;
    contain: layout style;

    &--no-data {
      padding: 10px 20px;
    }

    &--empty {
      padding-left: 17px;
      text-align: center;
    }
  }

  &__tracking-timeline {
    margin: 0;
    padding: 0;
    list-style: none;
    transform: translateZ(0);
  }

  &__tracking-item {
    position: relative;
    padding-bottom: 22px;
    font-size: @font-size-14;
    line-height: 20px;
    text-align: left;
    color: @text-color-tertiary;

    &::before {
      content: '';
      position: absolute;
      z-index: 2;
      left: -32px;
      display: block;
      width: 8px;
      height: 8px;
      background-color: @text-color-disabled;
      background-size: 100% 100%;
      border: 2px solid @bg-color-white;
      border-radius: 50%;
    }

    &::after {
      content: '';
      position: absolute;
      z-index: 1;
      left: -26px;
      top: 0;
      display: block;
      width: 1px;
      height: 100%;
      background: @divider-color-base;
    }

    &:first-child {
      color: @text-color-primary;

      &::before {
        left: -34px;
        width: 12px;
        height: 12px;
        background-color: @color-orange;
        border: 3px solid #ffd6b5;
      }
    }

    &:last-child::after {
      display: none;
    }
  }

  &__tracking-text {
    display: block;
    margin-bottom: 4px;
  }

  &__tracking-time {
    display: block;
    color: @text-color-tertiary;
    font-size: @font-size-12;
  }


  &__query-guide {
    position: relative;
    padding-bottom: 22px;
    font-size: @font-size-14;
    line-height: 20px;
    text-align: left;
    color: @text-color-primary;

    &::before {
      content: '';
      position: absolute;
      z-index: 2;
      left: -32px;
      display: block;
      width: 20px;
      height: 20px;
      background-size: 100% 100%;
    }
  }

  &__guide-text {
    margin-bottom: 10px;
    line-height: 1.5;
  }

  &__guide-note {
    line-height: 1.3;
    font-size: @font-size-13;
    color: @text-color-tertiary;
  }

  &__external-link {
    text-decoration: underline;
    color: @text-color-primary;

    &:visited,
    &:active {
      color: @text-color-primary;
    }

    &:hover {
      color: @color-orange;
    }
  }

  &__empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
  }

  &__empty-icon {
    margin: 31px auto 20px;
    width: 208px;
    height: 161px;
    background: @bg-color-white url(./assets/empty.png) no-repeat center;
    background-size: contain;

    // 预加载关键图片
    &::before {
      content: '';
      display: block;
      background-image: url('./assets/empty.png');
      width: 0;
      height: 0;
      opacity: 0;
    }
  }

  &__empty-text {
    line-height: 1.3;
    font-size: @font-size-13;
    color: @text-color-tertiary;
    text-align: center;
    margin: 0;
  }

  &__tracking-item--skeleton {
    &::before {
      background-color: #e0e0e0;
      animation: skeleton-loading 1.5s infinite;
    }
  }

  &__skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;

    &--text {
      height: 20px;
      width: 85%;
      margin-bottom: 4px;

      &:nth-child(odd) {
        width: 75%;
      }
    }

    &--time {
      height: 12px;
      width: 120px;
    }
  }
}

// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}
</style>
