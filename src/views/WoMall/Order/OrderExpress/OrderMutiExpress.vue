<template>
  <div class="multi-express">
    <div v-if="isLoading" class="multi-express__skeleton">
      <div class="skeleton-notice"></div>
      <div class="skeleton-item" v-for="n in 2" :key="n">
        <div class="skeleton-header"></div>
        <div class="skeleton-content"></div>
      </div>
    </div>

    <template v-else>
      <div class="multi-express__notice" v-if="shouldShowNotice">
        <i class="multi-express__notice-icon"></i>
        <span class="multi-express__notice-text">您订单中的商品已拆成{{ expressList.length }}个包裹发出</span>
      </div>
      <div class="multi-express__list">
        <article class="package-item" v-for="(item, index) in expressList" :key="`package-${index}`"
          @click="onExpressClick(index)">

          <header class="package-item__header">
            <div class="package-item__info">
              <i class="package-item__icon"></i>
              <span class="package-item__title">包裹 {{ index + 1 }}</span>
              <span class="package-item__status package-item__status--jd" v-if="isJDGoods(item)">
                {{ toJDExpressState(item.orderState) }}
              </span>
              <span class="package-item__status"
                v-else-if="get(item, 'deliverInfo.expressState') && toExpressState(item.deliverInfo.expressState)">
                {{ toExpressState(item.deliverInfo.expressState) }}
              </span>
            </div>
            <div class="package-item__express">
              <span class="package-item__express-info">
                <template v-if="get(item, 'deliverInfo.expressName')">
                  {{ item.deliverInfo.expressName }}：{{ item.deliverInfo.expressNo }}
                </template>
                <template v-else>暂无快递信息</template>
              </span>
              <i v-if="get(item, 'deliverInfo.expressName')" class="package-item__arrow"></i>
            </div>
          </header>

          <div class="package-item__content">
            <ul class="goods-list">
              <li class="goods-list__item" v-for="(goods, goodsIdx) in toSkuNumInfoList(item)"
                :key="`goods-${index}-${goodsIdx}`">
                <div class="goods-card">
                  <img class="goods-card__image"
                    :src="get(goods, 'sku.detailImageUrl') || get(goods, 'sku.listImageUrl')" loading="lazy"
                    decoding="async" :alt="`商品${goodsIdx + 1}`" />
                  <div class="goods-card__tag goods-card__tag--gift" v-if="get(goods, 'sku.skuType') === '6'">
                    赠品
                  </div>
                  <div class="goods-card__tag goods-card__tag--accessory" v-if="get(goods, 'sku.skuType') === '7'">
                    附件
                  </div>
                </div>
              </li>
            </ul>
          </div>

          <footer class="package-item__footer">
            <div class="package-summary">
              <span class="package-summary__total">共{{ toTotalCount(item) }}件商品</span>
            </div>
          </footer>
        </article>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, shallowRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showLoadingToast, closeToast } from 'vant'
import { getOrderExpress } from '@/api/interface/order'
import orderState from '@/utils/orderState'
import { JD_GOODS_CODE } from '@utils/types.js'
import { get, flatMap, debounce } from 'lodash-es'

const route = useRoute()
const router = useRouter()

const orderId = ref('')
const orderExpress = shallowRef({})
const isLoading = ref(true)

const expressList = computed(() => {
  if (!orderExpress.value || typeof orderExpress.value !== 'object') return []

  const orderPackageList = get(orderExpress.value, 'orderPackageList', [])
  const notDelivered = get(orderExpress.value, 'notDelivered', [])
  return [...orderPackageList, ...notDelivered]
})

const shouldShowNotice = computed(() => {
  return expressList.value.length > 1
})

const JD_CODE_REGEX = new RegExp(JD_GOODS_CODE)

const isJDGoods = (item) => {
  const supplierCode = get(item, 'supplierSubOrderList[0].supplier.code', '')
  return JD_CODE_REGEX.test(supplierCode)
}

const getOrderExpressData = async () => {
  try {
    showLoadingToast()
    const [err, json] = await getOrderExpress(orderId.value)
    closeToast()

    if (!err && json) {
      return json
    }
    return {}
  } catch (error) {
    console.error('获取订单快递信息失败:', error)
    return {}
  }
}

const onExpressClick = debounce((index) => {
  const item = expressList.value[index]
  const deliverInfo = get(item, 'deliverInfo')

  if (deliverInfo?.expressName) {
    router.push({
      name: 'user-order-express',
      query: {
        orderId: orderId.value,
        supplierSubOrderId: deliverInfo.supplierSubOrderId,
        expressNo: deliverInfo.expressNo
      }
    })
  }
}, 300)

const toSkuNumInfoList = (item) => {
  if (!item) return []

  if (item.supplierSubOrderList?.length) {
    return flatMap(item.supplierSubOrderList, subOrder => subOrder.skuNumInfoList || [])
  }
  return item.skuNumInfoList || []
}

const toTotalCount = (item) => {
  return get(item, 'deliverInfo.sendNum', 0)
}

const EXPRESS_STATE_MAP = new Map([
  ['0', '运输中'],
  ['1', '已揽件'],
  ['2', '疑难件'],
  ['3', '已签收'],
  ['4', '退签'],
  ['5', '派送中'],
  ['6', '退回'],
  ['7', '转单'],
  ['10', '待清关'],
  ['11', '清关中'],
  ['12', '已清关'],
  ['13', '清关异常'],
  ['14', '收件人拒签']
])

const toExpressState = (expressState) => {
  return EXPRESS_STATE_MAP.get(String(expressState)) || '待揽件'
}

const toJDExpressState = (state) => {
  return orderState(state)
}

onMounted(async () => {
  orderId.value = route.query.orderId
  const orderExpressParam = route.params.orderExpress

  if (orderExpressParam) {
    orderExpress.value = orderExpressParam
    isLoading.value = false
  } else {
    try {
      const data = await getOrderExpressData()
      orderExpress.value = data
    } finally {
      isLoading.value = false
    }
  }
})

onUnmounted(() => {
  closeToast()
})
</script>

<style lang="less" scoped>
.multi-express {
  &__notice {
    display: flex;
    align-items: center;
    padding: 7px 9px;
    background: #FFF5EC;

    &-icon {
      width: 20px;
      height: 20px;
      background-image: url(./assets/icon-ring.png);
      background-repeat: no-repeat;
      background-size: 100%;
    }

    &-text {
      margin-left: 2.5px;
      font-size: @font-size-13;
      color: @color-orange;
    }
  }

  &__list {
    margin-top: -10px;
    background-color: #f7f7f7cc;
  }
}

.package-item {
  margin-top: 10px;
  background-color: @bg-color-white;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 17px;
    border-bottom: 1px solid #e5e5e5a3;
  }

  &__info {
    display: flex;
    align-items: center;
    padding: 10px 0;
  }

  &__express {
    display: flex;
    align-items: center;
    padding: 10px 0;

    &-info {
      margin-right: 5px;
      font-size: @font-size-13;
      color: @text-color-secondary;
    }
  }

  &__icon {
    width: 15px;
    height: 15px;
    background-image: url(./assets/icon-goods.png);
    background-repeat: no-repeat;
    background-size: 100%;
  }

  &__title {
    margin-left: 3px;
    font-size: @font-size-14;
    color: @text-color-primary;
  }

  &__status {
    padding: 1px 3px;
    border: 1px solid @color-orange;
    border-radius: @radius-2;
    font-size: 9px;
    color: @color-orange;
    transform: scale(0.8);
    box-sizing: border-box;
  }

  &__arrow {
    width: 17px;
    height: 17px;
    background-image: url(./assets/arrow.png);
    background-repeat: no-repeat;
    background-size: 100%;
  }

  &__content {
    margin: 0 15px;
    overflow: hidden;
  }

  &__footer {
    padding: 0 17px 15px;
    font-size: @font-size-13;
    color: @text-color-primary;
  }
}

.goods-list {
  display: flex;
  overflow-x: scroll;
  margin-left: -15px;
  padding: 15px 0;

  &__item {
    padding-left: 15px;
    display: flex;
  }
}

.goods-card {
  position: relative;

  &__image {
    position: relative;
    display: block;
    width: 60px;
    height: 60px;
    line-height: 1;
    font-size: 12px;
    background-color: #eee;
  }

  &__tag {
    box-sizing: border-box;
    position: absolute;
    top: 0;
    right: 0;
    padding: 2px 3px;
    background-color: #f2f2f2;
    font-size: @font-size-11;
    color: @color-red;
    border: 1px solid @color-orange;
    border-radius: @radius-2;
  }
}

// 包裹统计
.package-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 骨架屏样式 - 优化 LCP
.multi-express__skeleton {
  .skeleton-notice {
    height: 34px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    margin-bottom: 10px;
  }

  .skeleton-item {
    background-color: @bg-color-white;
    margin-bottom: 10px;

    .skeleton-header {
      height: 50px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
      margin: 0 17px;
      border-bottom: 1px solid #e5e5e5a3;
    }

    .skeleton-content {
      height: 90px;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
      margin: 15px;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}

// 图片优化
.goods-card__image {
  // 添加 contain 布局优化
  contain: layout style paint;
  // 优化图片渲染
  image-rendering: -webkit-optimize-contrast;
  // 防止布局偏移
  aspect-ratio: 1;
  object-fit: cover;
}
</style>
