<template>
  <div class="search-entry">
    <SearchHeader ref="searchHeader" v-model="searchKeyword" placeholder="搜索商品" @search="handleSearch">
    </SearchHeader>

    <div class="search-history" v-if="historyRecords.length > 0">
      <div class="history-header">
        <span class="title">搜索历史</span>
        <div class="clear-all" @click="clearAllHistory">
          <img src="@/static/images/delete.png" alt="清空" class="delete-icon" />
        </div>
      </div>
      <div class="history-list">
        <div
          v-for="(item, index) in historyRecords"
          :key="index"
          class="history-item"
        >
          <span class="item-text" @click="useHistoryKeyword(item)">{{ item }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import SearchHeader from '@components/Common/SearchHeader.vue'
import { onMounted, ref } from 'vue'
import { delHistoryRecord, getOrderSearchHistory } from '@api/interface/search.js'
import { useRoute, useRouter } from 'vue-router'
import { get } from 'lodash-es'
import { closeToast, showLoadingToast } from 'vant'

const route = useRoute()
const router = useRouter()
const searchKeyword = ref('')
const historyRecords = ref([])
const searchHeader = ref(null)

// 获取搜索历史记录
const fetchHistoryRecords = async () => {
  showLoadingToast()
  const [err, data] = await getOrderSearchHistory()
  closeToast()
  if (!err && data) {
    historyRecords.value = data
  }
}

// 使用历史记录中的关键词
const useHistoryKeyword = (keyword) => {
  searchKeyword.value = keyword
  // handleSearch()
}

// 删除单个历史记录
const deleteHistoryItem = async (content) => {
  const [err] = await delHistoryRecord({
    type: 'SINGLE',
    content
  })
  if (!err) {
    await fetchHistoryRecords()
  }
}

// 清空所有历史记录
const clearAllHistory = async () => {
  const [err] = await delHistoryRecord({
    type: 'ALL'
  })
  if (!err) {
    historyRecords.value = []
  }
}

const handleSearch = () => {
  const testDMX = get(route.query, 'testDMX', false)
  if (searchKeyword.value.trim()) {
    // 跳转到搜索列表页面，并携带关键词参数
    router.push({
      path: '/user/order/searchList',
      query: {
        keyword: searchKeyword.value.trim(),
        testDMX
      }
    })
    // 搜索后刷新历史记录
    fetchHistoryRecords()
  }
}

onMounted(() => {
  fetchHistoryRecords()
  searchHeader.value.inputRef.focus()
})
</script>
<style scoped lang="less">
.search-entry {
  // 保持空白
}

.search-history {
  padding: @padding-page * 2;

  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .title {
      font-size: @font-size-15;
      font-weight: @font-weight-500;
      color: @text-color-primary;
    }

    .clear-all {
      display: flex;
      align-items: center;
      color: @text-color-tertiary;
      font-size: @font-size-14;

      .delete-icon {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }
  }

  .history-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .history-item {
      display: flex;
      align-items: center;
      padding: 3px 10px;
      background-color: @bg-color-gray;
      border-radius: @radius-2;
      .multi-ellipsis(2);

      .item-text {
        font-size: @font-size-13;
        color: @text-color-secondary;
        margin-right: 5px;
        .multi-ellipsis(2)
      }

      .delete-icon {
        width: 14px;
        height: 14px;
        opacity: @opacity-065;
      }
    }
  }
}
</style>
