<template>
  <div class="order-callback-page">
    <header class="order-callback-header">
      <div class="payment-status">
        <span class="status-icon" aria-label="支付成功图标"/>
        <span class="status-text">支付完成</span>
      </div>
      <div class="action-buttons">
        <WoButton
          size="medium"
          @click="onGoHomeClick">
          返回首页
        </WoButton>
        <WoButton
          type="primary"
          size="medium"
          class="view-order-btn"
          @click="onViewOrderClick">
          查看订单
        </WoButton>
      </div>
      <p class="order-tips">正在为您备货，请在我的订单中查询物流详情。</p>
    </header>
    <!-- 推荐商品区域预留，已注释但保持结构 -->
    <!--
    <section class="goods-recommend" v-if="hasGoodsList">
      <img class="recommend-title" src="./assets/recommend.png" alt="精选推荐" loading="lazy">
      <div class="goods-list">
        <Goods
          v-for="item in goodsList"
          :key="item.goodsId"
          :img="item.listImageUrl"
          :title="get(item, 'skuList[0].name')"
          :desc="specs(item)"
          :amt="price(item)"
          descColorful
          :numPerLine="2"
          @click="onGoodsClick(item)"/>
      </div>
    </section>
    -->
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { get, compact, isEmpty } from 'lodash-es'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'

const router = useRouter()
const goodsList = ref([])

const hasGoodsList = computed(() => !isEmpty(goodsList.value))

// 商品售价计算 =
const price = (goods) => {
  const sku = get(goods, 'skuList[0]')
  const promotionList = get(sku, 'skuPromotionList', [])
  return promotionList.length > 0
    ? get(promotionList, '[0].promotionPrice')
    : get(sku, 'price')
}

// 商品规格拼接
const specs = (goods) => {
  const sku = get(goods, 'skuList[0]')
  const params = [
    get(sku, 'param'),
    get(sku, 'param1'),
    get(sku, 'param2'),
    get(sku, 'param3'),
    get(sku, 'param4')
  ]
  return compact(params).join(' ')
}

// 获取商品列表数据
const goodsListData = async () => {
  const bizCode = getBizCode()
  let id = ''

  if (bizCode === 'ziying') {
    id = import.meta.env.VITE_CART_ZIYING_RECOMMEND_POOL_CODE
  } else if (bizCode === 'fupin') {
    id = import.meta.env.VITE_FP_HOME_PAGE_LIMITED_GOODS_ID
  }

  if (isEmpty(id)) return []

  try {
    const [err, json] = await getGoodsList({
      type: 'partion',
      bizCode: getBizCode('GOODS'),
      page_no: 1,
      page_size: 6,
      id
    })
    return !err ? json : []
  } catch (error) {
    console.error('获取商品列表失败:', error)
    return []
  }
}

// 返回首页
const onGoHomeClick = () => {
  router.push('/home')
}

// 查看订单
const onViewOrderClick = () => {
  router.push({
    path: '/user/order/list',
    query: { type: 3 }
  })
}

// 点击商品
const onGoodsClick = (goods) => {
  router.push(`/goodsdetail/${goods.id}`)
}

// 组件挂载时获取数据（如果需要推荐商品）
onMounted(async () => {
  // 暂时注释掉，避免不必要的API调用
  // goodsList.value = await goodsListData()
})
</script>

<style scoped lang="less">
.order-callback-page {
  min-height: 100vh;
  background-color: #fff;
}

.order-callback-header {
  padding: 25px 16px;
  text-align: center;
}

.payment-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 25px;
}

.status-icon {
  display: block;
  width: 28px;
  height: 28px;
  background-image: url(./assets/icon-success.png);
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.status-text {
  margin-left: 7px;
  font-size: 17px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-bottom: 16px;
}

.view-order-btn {
  /* 移除内联样式，使用class */
}

.order-tips {
  margin: 0;
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

/* 推荐商品区域样式（预留） */
.goods-recommend {
  background-color: #f8f9fa;
  padding: 20px 0;
  text-align: center;
}

.recommend-title {
  display: block;
  margin: 0 auto 16px;
  width: 213px;
  height: auto;
}

.goods-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 4px 17px 17px;
  gap: 8px;
}
.status-icon {
  will-change: transform;
}
</style>