<template>
  <div class="order-detail">
    <!-- 骨架屏 -->
    <div v-if="loading" class="order-detail__skeleton">
      <!-- 订单状态头部骨架屏 -->
      <div class="order-detail__skeleton-header">
        <div class="skeleton-line skeleton-line--title"></div>
        <div class="skeleton-line skeleton-line--subtitle"></div>
      </div>

      <div class="order-detail__skeleton-content">
        <!-- 物流状态模块骨架屏 -->
        <div class="skeleton-card">
          <div class="skeleton-logistics">
            <div class="skeleton-logistics__timeline">
              <div class="skeleton-circle"></div>
              <div class="skeleton-line--vertical"></div>
              <div class="skeleton-circle"></div>
            </div>
            <div class="skeleton-logistics__content">
              <div class="skeleton-line skeleton-line--logistics-title"></div>
              <div class="skeleton-line skeleton-line--logistics-detail"></div>
              <div class="skeleton-line skeleton-line--logistics-address"></div>
            </div>
          </div>
        </div>

        <!-- 地址模块骨架屏 -->
        <div class="skeleton-card">
          <div class="skeleton-address">
            <div class="skeleton-address__header">
              <div class="skeleton-line skeleton-line--address-user"></div>
              <div class="skeleton-line skeleton-line--address-action"></div>
            </div>
            <div class="skeleton-line skeleton-line--address-detail"></div>
          </div>
        </div>

        <!-- 商品模块骨架屏 -->
        <div class="skeleton-card">
          <div class="skeleton-goods">
            <div class="skeleton-goods__image"></div>
            <div class="skeleton-goods__info">
              <div class="skeleton-line skeleton-line--goods-title"></div>
              <div class="skeleton-line skeleton-line--goods-spec"></div>
              <div class="skeleton-line skeleton-line--goods-price"></div>
            </div>
          </div>
        </div>

        <!-- 价格信息模块骨架屏 -->
        <div class="skeleton-card">
          <div class="skeleton-price">
            <div class="skeleton-line skeleton-line--price-row"></div>
            <div class="skeleton-line skeleton-line--price-row"></div>
            <div class="skeleton-line skeleton-line--price-total"></div>
          </div>
        </div>

        <!-- 订单信息模块骨架屏 -->
        <div class="skeleton-card">
          <div class="skeleton-order">
            <div class="skeleton-line skeleton-line--info-row"></div>
            <div class="skeleton-line skeleton-line--info-row"></div>
            <div class="skeleton-line skeleton-line--info-row"></div>
            <div class="skeleton-line skeleton-line--info-row"></div>
          </div>
        </div>

        <!-- 底部操作按钮骨架屏 -->
        <div class="skeleton-actions">
          <div class="skeleton-button"></div>
          <div class="skeleton-button skeleton-button--primary"></div>
        </div>
      </div>
    </div>

    <!-- 订单状态头部 -->
    <header v-else class="order-detail__header">
      <div class="order-status">
        <h2 class="order-status__title">{{ statusTitle }}</h2>
        <div class="order-status__subtitle">
          <span class="order-status__text" v-if="subtitleText">{{ subtitleText }}</span>
          <span class="order-status__time" v-if="subtitleTime">{{ subtitleTime }}</span>
          <span class="order-status__text" v-if="subtitleSuffix">{{ subtitleSuffix }}</span>
        </div>
      </div>
    </header>

    <main v-if="!loading" class="order-detail__main">
      <!-- 物流状态模块 -->
      <WoCard v-if="shouldShowLogisticsModule">
        <div class="logistics-section">
          <!-- 左侧状态图标区域 -->
          <div class="logistics-timeline" v-if="currentOrderStatus !== ORDER_STATE.COMPLETED">
            <div class="logistics-timeline__item">
              <div class="logistics-timeline__dot logistics-timeline__dot--received"></div>
              <div class="logistics-timeline__line" :style="{ minHeight: 25 + 'px' }"></div>
              <div class="logistics-timeline__dot logistics-timeline__dot--pending"></div>
            </div>
          </div>

          <!-- 右侧物流信息区域 -->
          <div class="logistics-content">
            <div class="logistics-status" @click="handleViewLogistics">
              <div class="logistics-status__content">
                <div class="logistics-status__header">
                  <img src="../assets/package.png" alt="物流图标" class="logistics-status__icon" />
                  <span class="logistics-status__text">
                    {{ logisticsDisplayInfo.statusText }}
                  </span>
                  <span class="logistics-status__detail" v-if="lastOrderTrack">
                    {{ lastOrderTrack.context || '暂无物流信息' }}
                  </span>
                  <span class="logistics-status__detail" v-else>已上传物流单号，暂无快递信息</span>
                </div>
                <div class="logistics-status__arrow" v-if="logisticsDisplayInfo.showArrow">
                  <img src="../assets/arrow-black.png" alt="查看详情" class="arrow-icon" />
                </div>
              </div>
            </div>
            <div class="logistics-receiver" v-if="currentOrderStatus !== ORDER_STATE.COMPLETED">
              <div class="logistics-receiver__info">
                <div class="logistics-receiver__name">{{ receiverInfo.name }}</div>
                <div class="logistics-receiver__phone">{{ receiverInfo.phone }}</div>
              </div>
              <div class="logistics-receiver__address">
                {{ fullAddress }}
              </div>
            </div>
          </div>
        </div>
      </WoCard>

      <!-- 地址模块 -->
      <WoCard v-if="orderInfo.addressInfo && shouldShowAddressModule">
        <div class="address-section">
          <div class="address-section__header">
            <div class="address-user">
              <img src="@/static/images/address-icon.png" alt="地址图标" class="address-user__icon" />
              <span class="address-user__name">{{ receiverInfo.name }}</span>
              <span class="address-user__phone">{{ receiverInfo.phone }}</span>
            </div>
            <div class="address-action">
              <div class="address-action__button">
                <img src="@/static/images/mod-address-icon.png" alt="地址编辑" class="address-action__icon" />
                修改地址
              </div>
            </div>
          </div>
          <div class="address-section__content">
            <div class="address-detail">
              {{ fullAddress }}
            </div>
          </div>
        </div>
      </WoCard>

      <!-- 商品模块 -->
      <WoCard v-if="transformedGoodsList.length > 0">
        <GoodsListItem v-for="(goodsItem, index) in transformedGoodsList" :key="goodsItem.id || index" :item="goodsItem"
          :item-id="goodsItem.id || index" :image-size="90" :min-height="110" :show-actions="true">
          <template #actions="{ item }">
            <WoButton v-for="action in getItemActions(item)" :key="action.key" size="small"
              :type="action.type || 'primary'" @click="action.handler(item)">
              {{ action.label }}
            </WoButton>
          </template>
        </GoodsListItem>
      </WoCard>

      <WoCard v-if="orderInfo">
        <InfoRow label="商品总价">
          <template #value>
            <PriceDisplay :price="orderInfo.orderPrice" size="small" />
          </template>
        </InfoRow>
        <InfoRow v-if="isJD && totalFreight" label="运费" value="运费已分摊至商品金额" />
        <InfoRow v-else label="运费">
          <template #value>
            <PriceDisplay :price="formatMoney(0)" size="small" :bold="false" />
          </template>
        </InfoRow>
        <InfoRow :label="priceLabel">
          <template #value>
            <PriceDisplay :price="orderInfo.orderPrice" size="medium" :color="priceColor" />
          </template>
        </InfoRow>
      </WoCard>

      <WoCard v-if="orderInfo">
        <InfoRow label="交付订单号">
          <template #value>
            <div class="order-number">
              <span class="order-number__text">{{ orderInfo.bizOrderId }}</span>
              <span class="order-number__copy" @click="copyText(orderInfo.bizOrderId)">复制</span>
            </div>
          </template>
        </InfoRow>
        <InfoRow label="收货信息" v-if="orderInfo.addressInfo && currentOrderStatus === ORDER_STATE.COMPLETED">
          <template #value>
            <div class="receiver-info">
              <div class="receiver-info__contact">{{ receiverInfo.name }} {{ receiverInfo.phone }}</div>
              <div class="receiver-info__address">{{ fullAddress }}</div>
            </div>
          </template>
        </InfoRow>
        <InfoRow label="下单时间" :value="formatDate(orderInfo.orderDate)" v-if="orderInfo.orderDate" />
        <InfoRow label="交易时间" :value="formatDate(orderInfo.orderDate)" v-if="orderInfo.orderDate" />
        <InfoRow label="付款方式" value="在线支付" />
      </WoCard>

      <WoCard v-if="hasDeposit">
        <div class="deposit-section" @click="handleViewDeposit">
          <div class="deposit-section__info">
            <img src="@/static/images/baozhengjin.png" alt="保证金图标" class="deposit-section__icon" />
            <span class="deposit-section__label">保证金</span>
          </div>
          <div class="deposit-section__amount">
            <span class="deposit-amount">{{ depositAmount }}元存期{{ depositPeriod }}年</span>
          </div>
          <div class="deposit-section__action" @click="handleViewDeposit">
            <span class="deposit-action__text">查看详情</span>
            <img src="@/static/images/arrow-right-gray.png" alt="查看详情" class="deposit-action__icon" />
          </div>
        </div>
      </WoCard>

      <WoActionBarPlaceholder />

      <!-- 底部操作按钮  -->
      <WoActionBar>
        <div class="action-bar"
          :class="{ 'action-bar--pending-payment': currentOrderStatus === ORDER_STATE.PENDING_PAYMENT }">
          <WoButton v-for="action in getBottomActions(currentOrderStatus)" :key="action.key" :type="action.type"
            :size="currentOrderStatus === ORDER_STATE.PENDING_PAYMENT ? 'large' : 'medium'" @click="action.handler">
            <template v-if="action.key === 'goToPay' && currentOrderStatus === ORDER_STATE.PENDING_PAYMENT">
              <span style="margin-right: 5px;">去支付</span>
              <PriceDisplay :price="orderInfo.orderPrice" size="small" color="white" />
            </template>
            <template v-else>
              {{ action.label }}
            </template>
          </WoButton>
        </div>
      </WoActionBar>
    </main>

    <!-- 售后过期提示弹窗 -->
    <ExpirationPopup v-model:visible="expirationPopupVisible" title="" main-text="抱歉，订单已过售后申请时效"
      sub-text="商品已超过售后期限，如需售后可联系客服处理" confirm-text="确定" @close="expirationPopupVisible = false"
      @confirm="expirationPopupVisible = false" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, shallowRef, markRaw } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { get, reduce, map, filter, some, find, isEmpty, isEqual } from 'lodash-es'
import PriceDisplay from '@/components/Common/PriceDisplay.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import GoodsListItem from '@components/Common/GoodsListItem.vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import InfoRow from '@components/Common/InfoRow.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import orderStatus from '@/utils/orderState.js'
import { getOrderInfo, getOrderExpress, getExpress, modOrderListShow, cancelOrder, repayOrder } from '@/api/interface/order.js'
import { addOneClick } from '@/api/interface/goods.js'
import { isCheckGoodsExistsBol } from '@/api/interface/order.js'
import { getBizCode } from '@/utils/curEnv.js'
import dayjs from 'dayjs'
import useClipboard from 'vue-clipboard3'
import { formSubmit } from 'commonkit'
import {
  analyzeLogisticsStatus,
  isFinalStatus,
} from '@/utils/logisticsStatusAnalyzer.js'
import { useAlert } from "@/hooks/index.js";
import { useUserStore } from "@store/modules/user.js";
import { useAfterSalesStore } from "@store/modules/afterSales.js";
import ExpirationPopup from '@/components/Common/ExpirationPopup/ExpirationPopup.vue';
import {
  applyOrderAfterSalesJD,
  getOrderDetailsAfterSalesButton,
  getSupplierSubOrderIdToAfterSaleId
} from '@/api/interface/order.js';
import { applyOrderCancel, jdPrompt } from '@/api/interface/afterSales.js';
import { afterSalesProduct } from '@/utils/storage.js';

const { toClipboard } = useClipboard()
const router = useRouter()
const route = useRoute()
const $alert = useAlert()
const userStore = useUserStore()
const afterSalesStore = useAfterSalesStore()

// 售后相关状态
const expirationPopupVisible = ref(false)
const afterSalesInfo = computed(() => afterSalesStore.getAfterSalesInfo)

// 订单状态枚举 - 使用 markRaw 避免响应式包装
const ORDER_STATE = markRaw({
  PENDING_PAYMENT: '0', // 待付款
  PENDING_DELIVERY_1: '1', // 待发货 (部分供应商支付成功需要确认库存)
  CANCELLED: '2', // 已取消
  PENDING_DELIVERY_3: '3', // 待发货
  PARTIAL_SHIPPED: '4', // 部分发货
  SHIPPING: '5', // 配送中
  PARTIAL_CANCELLED: '6', // 部分撤销 (前端无此状态)
  REJECTED: '7', // 拒收
  REVOKED: '8', // 已撤销
  COMPLETED: '9', // 已签收
  REFUNDED: '10', // 已退款
  PARTIAL_REFUNDED: '11', // 部分退款
  PARTIAL_REFUNDING: '12', // 部分退款中 (前端无此状态)
  DELETED: '-1' // 已删除
})

// 预计算状态集合，避免重复计算
const LOGISTICS_ALLOWED_STATUSES = markRaw([ORDER_STATE.REFUNDED, ORDER_STATE.COMPLETED, ORDER_STATE.SHIPPING])
const ADDRESS_ALLOWED_STATUSES = markRaw([ORDER_STATE.PENDING_PAYMENT, ORDER_STATE.PENDING_DELIVERY_1, ORDER_STATE.PENDING_DELIVERY_3, ORDER_STATE.CANCELLED])

// 判断是否显示物流状态模块 - 使用预计算的状态集合
const shouldShowLogisticsModule = computed(() => {
  return LOGISTICS_ALLOWED_STATUSES.indexOf(currentOrderStatus.value) !== -1
})

// 判断是否显示地址模块 - 使用预计算的状态集合
const shouldShowAddressModule = computed(() => {
  return ADDRESS_ALLOWED_STATUSES.indexOf(currentOrderStatus.value) !== -1
})
// 计算运费总额 - 使用 lodash reduce 优化
const totalFreight = computed(() => {
  const state = currentOrderStatus.value
  if (state === ORDER_STATE.PENDING_PAYMENT) {
    return get(orderInfo.value, 'totalFreight', 0)
  }

  return reduce(orderDetailSupplierOrderVoList.value, (sum, item) => sum + (item.freight || 0), 0)
})

// 获取价格标签
const priceLabel = computed(() => {
  if (currentOrderStatus.value === ORDER_STATE.PENDING_PAYMENT || currentOrderStatus.value === ORDER_STATE.CANCELLED) {
    return '应付款'
  }
  return '实付款'
})

// 获取价格颜色
const priceColor = computed(() => {
  if (currentOrderStatus.value === ORDER_STATE.PENDING_PAYMENT) {
    return 'orange'
  }
  return undefined // 不传递color属性
})

const fullAddress = computed(() => {
  const addressInfo = get(orderInfo.value, 'addressInfo')
  if (!addressInfo) return ''

  const { provinceName, cityName, countyName, townName, addrDetail } = addressInfo

  // 使用 lodash filter 和 compact 优化过滤逻辑
  const addressParts = filter([provinceName, cityName, countyName, townName, addrDetail], part => part && part.trim())

  return addressParts.join('')
})

const receiverInfo = computed(() => {
  const addressInfo = get(orderInfo.value, 'addressInfo')
  if (!addressInfo) return { name: '', phone: '' }

  return {
    name: get(addressInfo, 'recName', ''),
    phone: get(addressInfo, 'recPhone', '')
  }
})

// 格式化金额
const formatMoney = (amount) => {
  return (amount / 100).toFixed(2)
}

// 当前订单状态 (通过API获取)
const currentOrderStatus = ref('')
// 倒计时相关
const countdown = ref({
  hours: 0,
  minutes: 30,
  seconds: 0
})

const receiptCountdown = ref({
  days: 12,
  hours: 12,
  minutes: 14
})

let countdownTimer = null

// 订单相关数据 - 使用 shallowRef 优化大对象性能
const orderId = ref(route.params.orderId || route.query.orderId)
const isPay = ref(route.query.isPay || 0)
const orderInfo = shallowRef({})
const orderDetailSupplierOrderVoList = shallowRef([])
const isJD = ref(false)
const isZST = ref(false)
const orderExpress = shallowRef({})
const orderPackageList = shallowRef([])
const orderTrack = shallowRef([])
const lastOrderTrack = shallowRef(null)
const logisticsAnalysis = shallowRef(null) // 物流状态分析结果
const dataLoaded = ref(false)
const loading = ref(true)

// 保证金相关
const hasDeposit = ref(false)
const depositOrderId = ref('')
const depositAmount = ref(0)
const depositPeriod = ref(0)

// 支付相关
const dialogShow = ref(false)
const wapay = ref({
  bizOrderId: '',
  encryptContent: '',
  wapURL: ''
})

// 转换后的商品列表数据 - 使用 lodash 链式操作优化
const transformedGoodsList = computed(() => {
  return reduce(orderDetailSupplierOrderVoList.value, (result, supplierOrder) => {
    const subOrderList = get(supplierOrder, 'orderDetailSupplierSubOrderVoList', [])

    const transformedItems = map(subOrderList, subOrder => {
      const sku = get(subOrder, 'skuNumInfoList[0]')
      const skuData = get(sku, 'sku', {})

      return {
        id: `${get(skuData, 'goodsId', '')}_${get(skuData, 'skuId', '')}`,
        rawData: supplierOrder,
        subOrderRawData: subOrder,
        price: get(skuData, 'price', 0),
        quantity: get(sku, 'skuNum', 0),
        detailImageUrl: get(skuData, 'detailImageUrl[0]', ''),
        orderState: get(subOrder, 'orderState', ''),
        skuNumInfoList: [{
          sku: {
            ...skuData,
            goodsId: get(skuData, 'goodsId', ''),
            skuId: get(skuData, 'skuId', ''),
            name: get(skuData, 'name', ''),
            detailImageUrl: get(skuData, 'detailImageUrl[0]', ''),
            param: get(skuData, 'param', ''),
            param1: get(skuData, 'param1', ''),
            param2: get(skuData, 'param2', ''),
            param3: get(skuData, 'param3', ''),
            param4: get(skuData, 'param4', ''),
            price: get(skuData, 'price', 0),
          },
          skuNum: get(sku, 'skuNum', 0)
        }]
      }
    })

    return result.concat(transformedItems)
  }, [])
})

// 获取状态标题
const statusTitle = computed(() => {
  return orderStatus(currentOrderStatus.value)
})

// 获取副标题文字前缀
const subtitleText = computed(() => {
  switch (currentOrderStatus.value) {
    case ORDER_STATE.PENDING_PAYMENT:
      return '剩 '
    case ORDER_STATE.SHIPPING:
      return '还剩 '
    default:
      return ''
  }
})

// 获取副标题时间部分
const subtitleTime = computed(() => {
  switch (currentOrderStatus.value) {
    case ORDER_STATE.PENDING_PAYMENT:
      return `${formatTime(countdown.value.hours)}:${formatTime(countdown.value.minutes)}:${formatTime(countdown.value.seconds)}`
    case ORDER_STATE.SHIPPING:
      return `${receiptCountdown.value.days}天${receiptCountdown.value.hours}小时${receiptCountdown.value.minutes}分`
    default:
      return ''
  }
})

// 获取副标题文字后缀
const subtitleSuffix = computed(() => {
  switch (currentOrderStatus.value) {
    case ORDER_STATE.PENDING_PAYMENT:
      return ' 支付关闭'
    case ORDER_STATE.CANCELLED:
      return '超时未支付自动关闭'
    case ORDER_STATE.SHIPPING:
      return ' 支付确认'
    default:
      return ''
  }
})

// 物流状态显示信息
const logisticsDisplayInfo = computed(() => {
  if (!logisticsAnalysis.value) {
    return {
      statusText: '暂无物流信息',
      statusColor: '#999999',
      showArrow: false,
      confidence: 0
    }
  }

  const analysis = logisticsAnalysis.value
  return {
    statusText: analysis.statusText,
    showArrow: !!lastOrderTrack.value,
    confidence: analysis.confidence,
    isFinal: isFinalStatus(analysis.status)
  }
})
// 格式化时间（补零）
const formatTime = (time) => {
  return time.toString().padStart(2, '0')
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return ''
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 复制文本功能
const copyText = async (text) => {
  try {
    await toClipboard(text);
    showToast('复制成功');
  } catch (e) {
    console.error(e);
    showToast('复制失败');
  }
}


// 启动倒计时
const startCountdown = (orderData = null) => {
  // 清除之前的倒计时
  clearCountdown()

  if (currentOrderStatus.value === ORDER_STATE.PENDING_PAYMENT) {
    let remainingTime = 0

    if (orderData) {
      // 根据订单信息计算剩余时间
      const orderTime = new Date(orderData.orderDate || orderData.createTime).getTime()
      const currentTime = new Date().getTime()
      const paymentTimeout = orderData.paymentTimeout || 30 * 60 * 1000 // 默认30分钟，单位毫秒

      remainingTime = Math.max(0, paymentTimeout - (currentTime - orderTime))
    } else {
      // 如果没有订单数据，使用默认30分钟
      remainingTime = 30 * 60 * 1000
    }

    // 将毫秒转换为时分秒
    const totalSeconds = Math.floor(remainingTime / 1000)
    countdown.value.hours = Math.floor(totalSeconds / 3600)
    countdown.value.minutes = Math.floor((totalSeconds % 3600) / 60)
    countdown.value.seconds = totalSeconds % 60

    // 如果时间已经过期，直接设置为取消状态
    if (remainingTime <= 0) {
      currentOrderStatus.value = ORDER_STATE.CANCELLED
      return
    }

    countdownTimer = setInterval(() => {
      if (countdown.value.seconds > 0) {
        countdown.value.seconds--
      } else if (countdown.value.minutes > 0) {
        countdown.value.minutes--
        countdown.value.seconds = 59
      } else if (countdown.value.hours > 0) {
        countdown.value.hours--
        countdown.value.minutes = 59
        countdown.value.seconds = 59
      } else {
        // 倒计时结束，订单自动取消
        currentOrderStatus.value = ORDER_STATE.CANCELLED
        clearInterval(countdownTimer)
      }
    }, 1000)
  }
}

// 清除倒计时
const clearCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
    countdownTimer = null
  }
}

// 获取订单信息
const getOrderInfoData = async () => {
  loading.value = true
  showLoadingToast()

  try {
    const [err, json] = await getOrderInfo(orderId.value, isPay.value)
    closeToast()

    if (!err && json) {
      orderInfo.value = json

      // 判断是否显示保证金信息，三者都存在才显示
      hasDeposit.value = !!(orderInfo.value.bondOrderId && orderInfo.value.bondPrice && orderInfo.value.bondTerm)

      if (orderInfo.value.bondOrderId) {
        depositOrderId.value = orderInfo.value.bondPrice
      }

      if (orderInfo.value.bondPrice) {
        depositAmount.value = (+orderInfo.value.bondPrice) / 100
      }

      if (orderInfo.value.bondTerm) {
        depositPeriod.value = (+orderInfo.value.bondTerm) / 12
      }

      // json.orderDetailSupplierOrderVoList 这个字段是个[]
      orderDetailSupplierOrderVoList.value = json.orderDetailSupplierOrderVoList || []

      // 获取订单状态
      if (orderInfo.value.orderState === '0') {
        currentOrderStatus.value = orderInfo.value.orderState
      } else {
        currentOrderStatus.value = orderDetailSupplierOrderVoList.value[0]?.orderState || orderInfo.value.orderState
      }

      // 获取是否为京东 - 使用 lodash some 优化
      isJD.value = some(orderDetailSupplierOrderVoList.value, item =>
        get(item, 'supplierCode', '').indexOf('jd_') > -1
      )

      isZST.value = some(orderDetailSupplierOrderVoList.value, item =>
        get(item, 'supplierCode', '').indexOf('zst') > -1
      )

      // 快递详情
      orderExpress.value = await getOrderExpressData()
      orderPackageList.value = orderExpress.value.orderPackageList

      // 如果有物流包裹，查询第一个包裹的物流信息 - 优化循环逻辑
      if (!isEmpty(orderPackageList.value)) {
        // 使用 find 替代 for 循环，更高效
        for (const packageItem of orderPackageList.value) {
          const deliverInfo = await getExpressData(
            get(packageItem, 'deliverInfo.supplierSubOrderId'),
            get(packageItem, 'deliverInfo.expressNo')
          )

          const trackData = get(deliverInfo, 'orderTrack', [])
          if (!isEmpty(trackData)) {
            orderTrack.value = trackData
            lastOrderTrack.value = trackData[0]
            logisticsAnalysis.value = analyzeLogisticsStatus(trackData)
            break
          }
        }
      }

      // 所有数据加载完成后设置标志
      dataLoaded.value = true

      // 在获取订单信息后启动倒计时，传入订单数据
      startCountdown(orderInfo.value)
    } else {
      showToast('获取订单信息失败')
    }
  } catch (error) {
    showToast.clear()
    showToast('获取订单信息失败')
    console.error('获取订单信息错误:', error)
  } finally {
    loading.value = false
  }
}

// 获取订单快递信息
const getOrderExpressData = async () => {
  showLoadingToast()
  const [err, json] = await getOrderExpress(orderId.value, isPay.value)
  closeToast()
  if (!err) return json
  return {}
}

// 查询快递信息
const getExpressData = async (orderIdParam, expressNo) => {
  showLoadingToast()
  const [err, json] = await getExpress(orderIdParam, expressNo)
  closeToast()
  if (!err) return json
  return {}
}

// 加购物车 - 单个商品
const handleAddToCart = async (item) => {
  try {
    // 从商品项中获取第一个sku信息
    const subOrder = item
    const { sku } = subOrder.skuNumInfoList[0]
    const { goodsId, skuId, supplierCode } = sku

    const params = {
      goodsId,
      skuId,
      supplierCode
    }

    if (!goodsId || !skuId) {
      showToast('部分商品无货或已下架无法添加购物车')
      return
    }

    showLoadingToast()

    // 检查商品是否存在
    const [err1, json] = await isCheckGoodsExistsBol(params)
    if (!err1) {
      if (json) {
        params.goodsId = json
      } else {
        closeToast()
        showToast('部分商品无货或已下架无法添加购物车!')
        return
      }
    } else {
      closeToast()
      showToast('部分商品无货或已下架无法添加购物车！')
      return
    }

    // 准备地址信息
    const info = userStore.curAddressInfo
    const addressInfo = JSON.stringify({
      provinceId: info.provinceId,
      provinceName: info.provinceName,
      cityId: info.cityId,
      cityName: info.cityName,
      countyId: info.countyId,
      countyName: info.countyName,
      townId: info.townId,
      townName: info.townName
    })

    // 调用加购物车API
    const [err2] = await addOneClick({
      ...params,
      bizCode: getBizCode('ORDER'),
      addressInfo: addressInfo
    })

    closeToast()

    if (err2) {
      showToast(err2.msg || '加入购物车失败')
      return
    }

    showToast('加入购物车成功！')
    console.log('加购物车商品:', item)
  } catch (error) {
    closeToast()
    showToast('加入购物车失败，请重试')
    console.error('加购物车错误:', error)
  }
}

// 判断是否过期 - 15天售后期限
const isExpires = (subOrder) => {
  const orderDate = get(subOrder, 'subOrderRawData.orderDate') || get(orderInfo.value, 'orderDate')
  if (!orderDate) return false

  const expDate = dayjs(orderDate).add(15, 'day')
  const now = dayjs()
  return expDate > now
}

// 申请售后/退款 - 单个商品
const handleAfterSalesAction = async (item, type) => {
  const subOrder = item.subOrderRawData
  const isExpired = !isExpires(item)
  const afterSaleApplyList = get(subOrder, 'afterSaleApplyList', [])
  const hasAfterSaleId = afterSaleApplyList.length > 0

  // 如果已过期且没有售后ID，显示过期提示
  if (isExpired && !hasAfterSaleId) {
    expirationPopupVisible.value = true
    return
  }

  const supplierSubOrderId = get(subOrder, 'id')
  const { orderPrice, orderState, id, supplierSubOutOrderId } = subOrder
  const { skuNum, sku } = get(subOrder, 'skuNumInfoList[0]', {})
  const { supplierCode } = sku || {}

  // 设置售后产品信息
  const afterSalesProductInfo = {
    supplierSubOrderId: id,
    orderState,
    orderPrice,
    skuNum,
    sku,
    supplierCode,
    supplierOutSubOrderId: supplierSubOutOrderId || ''
  }
  afterSalesProduct.set(afterSalesProductInfo)

  // 重置售后信息
  if (afterSalesInfo.value && afterSalesInfo.value.applyType) {
    afterSalesStore.updateAfterSalesInfo({
      applyType: '',
      afterSaleState: '',
      bizOrderId: '',
      bizCode: '',
      orderState: ''
    })
  }

  const isJDSupplier = supplierCode && supplierCode.indexOf('jd_') > -1
  const giftInfoList = get(subOrder, 'giftInfoList', [])
  const afterSaleId = afterSaleApplyList[0]?.id
  const applyType = afterSaleApplyList[0]?.applyType

  if (isJDSupplier) {
    await handleJDAfterSales(supplierSubOrderId, supplierCode, type, giftInfoList, afterSaleId, applyType)
  } else {
    await handleNonJDAfterSales(supplierSubOrderId, type, afterSaleId, applyType)
  }
}

// 处理京东售后
const handleJDAfterSales = async (supplierSubOrderId, supplierCode, type, giftInfoList, afterSaleId, applyType) => {
  try {
    const [err, res] = await jdPrompt({ supplierSubOrderId, supplierCode })
    if (err) {
      showToast(err.msg)
      return
    }

    const prompt = res

    if (currentOrderStatus.value === ORDER_STATE.COMPLETED) {
      // 已签收状态
      if (type === 1) { // 申请售后
        if (giftInfoList && giftInfoList.length > 0) {
          await $alert({
            title: '',
            message: '该商品有赠品，如申请售后，请将赠品一同寄回。',
            confirmButtonText: '确定申请',
            cancelButtonText: '暂不申请',
            showCancelButton: true,
            onConfirmCallback: () => {
              applyJDAfterSales(supplierSubOrderId, 1)
            }
          })
          return
        }
        await applyJDAfterSales(supplierSubOrderId, 1)
      } else if (type === 2) { // 查看详情
        await applyJDAfterSales(supplierSubOrderId, 2)
      }
    } else {
      // 其他状态
      if (type === 0) { // 申请退款
        await $alert({
          title: '',
          message: prompt,
          confirmButtonText: '确定申请',
          cancelButtonText: '暂不申请',
          showCancelButton: true,
          onConfirmCallback: () => {
            applyJDRefund(supplierSubOrderId)
          }
        })
      } else if (type === 1) { // 申请售后
        await applyJDAfterSales(supplierSubOrderId, 1)
      } else if (type === 2) { // 查看详情
        if (applyType === '1') {
          router.push({
            path: '/wo-after-sales-detail',
            query: {
              afterSaleId,
              type: +applyType
            }
          })
        } else {
          await applyJDAfterSales(supplierSubOrderId, 2)
        }
      }
    }
  } catch (error) {
    showToast('操作失败，请重试')
    console.error('京东售后处理错误:', error)
  }
}

// 处理非京东售后
const handleNonJDAfterSales = async (supplierSubOrderId, type, afterSaleId, applyType) => {
  try {
    if (type === 0) { // 申请退款
      await $alert({
        title: '',
        message: '您确定申请退款吗？',
        confirmButtonText: '确定申请',
        cancelButtonText: '暂不申请',
        showCancelButton: true,
        onConfirmCallback: () => {
          applyRefund(supplierSubOrderId)
        }
      })
    } else if (type === 1) { // 申请售后
      router.push({
        path: '/wo-after-sales-entry',
        query: {
          orderState: currentOrderStatus.value
        }
      })
    } else if (type === 2) { // 查看详情
      router.push({
        path: '/wo-after-sales-detail',
        query: {
          afterSaleId,
          type: +applyType
        }
      })
    }
  } catch (error) {
    showToast('操作失败，请重试')
    console.error('非京东售后处理错误:', error)
  }
}

// 京东退款申请
const applyJDRefund = async (supplierSubOrderId) => {
  try {
    showLoadingToast()
    const [err, res] = await applyOrderCancel({ supplierSubOrderId })
    closeToast()

    if (!err) {
      const afterSaleId = res
      router.push({
        path: '/wo-after-sales-detail',
        query: {
          afterSaleId,
          type: 1
        }
      })
    } else {
      showToast(err.msg)
    }
  } catch (error) {
    closeToast()
    showToast('申请退款失败')
    console.error('京东退款申请错误:', error)
  }
}

// 京东售后申请
const applyJDAfterSales = async (supplierSubOrderId, firstUrl) => {
  try {
    showLoadingToast()
    const [err, json] = await applyOrderAfterSalesJD({
      supplierSubOrderId,
      firstUrl
    })
    closeToast()

    if (!err) {
      window.location.href = json
    } else {
      showToast(err.msg)
    }
  } catch (error) {
    closeToast()
    showToast('申请售后失败')
    console.error('京东售后申请错误:', error)
  }
}

// 非京东退款申请
const applyRefund = async (supplierSubOrderId) => {
  try {
    showLoadingToast()
    const [err, res] = await applyOrderCancel({ supplierSubOrderId })
    closeToast()

    if (!err) {
      const afterSaleId = res
      router.push({
        path: '/wo-after-sales-detail',
        query: {
          afterSaleId,
          type: 1
        }
      })
    } else {
      showToast(err.msg)
    }
  } catch (error) {
    closeToast()
    showToast('申请退款失败')
    console.error('退款申请错误:', error)
  }
}

// 取消订单
const handleCancelOrder = async () => {
  const cancelOrderFn = async () => {
    showLoadingToast()
    const [err] = await cancelOrder(orderId.value)
    closeToast()
    if (!err) {
      // 清理当前订单的定时器
      clearCountdown()

      // 重新获取订单信息以更新状态
      await getOrderInfoData()

      showToast('订单取消成功')
    } else {
      showToast(err.msg || '取消订单失败')
    }
  }

  await $alert({
    title: '',
    message: '取消后将无法恢复，您确定要取消订单吗？',
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    showCancelButton: true,
    onConfirmCallback: async () => {
      await cancelOrderFn()
    }
  })
}


// 去支付
const handleGoToPay = async () => {
  showLoadingToast()
  try {
    const [res, json] = await repayOrder(orderId.value)
    closeToast()

    if (res.code === '0000') {
      if (getBizCode() === 'fupin' && json.isNeedCompanyInsert === 'true') {
        dialogShow.value = true
        wapay.value.encryptContent = json.encryptContent
        wapay.value.wapURL = json.wapURL
        wapay.value.bizOrderId = json.storeOrderId
      } else {
        formSubmit(json.wapURL, { param: json.encryptContent })
      }
    } else if (res.code === '2091070302' && !isEmpty(res.data)) {
      // 订单中有下架 or 库存不足 or 无货的商品 - 使用 lodash some 优化
      if (some(json, ['state', '2'])) {
        showToast('您的订单中有商品已下架')
      } else if (some(json, ['state', '3'])) {
        showToast('您的订单中有无货商品')
      } else if (some(json, ['state', '4'])) {
        showToast('您的订单中有商品库存不足')
      }
    } else {
      showToast(res.msg)
    }
  } catch (error) {
    closeToast()
    console.error('支付失败:', error)
    showToast('支付失败，请重试')
  }
}

// 删除订单
const handleDeleteOrder = async () => {
  const cancelOrderFn = async () => {
    showLoadingToast()

    try {
      // 如果有supplierOrderList，需要对每个供应商订单分别删除 - 使用 lodash map 优化
      if (!isEmpty(orderDetailSupplierOrderVoList.value)) {
        const deletePromises = map(orderDetailSupplierOrderVoList.value, supplierOrder =>
          modOrderListShow({
            supplierOrderId: supplierOrder.id,
            isDelete: 1
          })
        )

        await Promise.all(deletePromises)
      } else {
        console.warn(1232132321, orderId.value)
        const [err] = await modOrderListShow({
          supplierOrderId: orderId.value,
          isDelete: 1
        })
        if (err) {
          throw new Error(err.msg)
        }
      }

      closeToast()

      // 清理被删除订单的定时器
      clearCountdown(orderId.value)

      // 删除成功后的处理
      // 可以选择跳转回订单列表页面或显示删除成功提示
      showToast('订单删除成功')

      // 跳转回订单列表页面
      setTimeout(() => {
        router.back()
      }, 1500)

    } catch (error) {
      closeToast()
      showToast(error.message || '删除失败')
    }
  }

  try {
    await $alert({
      title: '',
      message: '确认删除该订单？',
      confirmButtonText: '确定',
      showCancelButton: true,
      cancelButtonText: '取消',
      onConfirmCallback: async () => {
        await cancelOrderFn()
      }
    })
  } catch (error) {
    // 用户点击取消或关闭弹窗
    console.log('用户取消删除操作')
  }
}


// 再次购买 - 使用 lodash map 优化
const handleBuyAgain = () => {
  try {
    // 获取订单中的商品信息
    const goodsItems = map(transformedGoodsList.value, item => {
      const sku = get(item, 'skuNumInfoList[0].sku', {})
      return {
        goodsId: get(sku, 'goodsId'),
        skuId: get(sku, 'skuId'),
        quantity: get(item, 'skuNumInfoList[0].skuNum')
      }
    })

    // TODO: 将商品加入购物车或直接跳转到商品详情页
    // if (goodsItems.length === 1) {
    //   // 单个商品直接跳转到商品详情页
    //   router.push({
    //     path: `/goods/detail/${goodsItems[0].goodsId}`,
    //     query: { skuId: goodsItems[0].skuId }
    //   })
    // } else {
    //   // 多个商品批量加入购物车
    //   await addMultipleToCart(goodsItems)
    //   router.push('/cart')
    // }

    showToast('再次购买功能待实现')
    console.log('再次购买商品:', goodsItems)
  } catch (error) {
    showToast('再次购买失败')
    console.error('再次购买错误:', error)
  }
}

const handleUrgeDelivery = () => {
  const { orderDate } = orderInfo.value
  const targetDate = dayjs(orderDate)
  const now = dayjs()
  // 计算两个时间之间的差异（毫秒）
  const diff = now.diff(targetDate, 'millisecond')
  // 将毫秒差异转换成小时
  const diffInHours = diff / (1000 * 60 * 60)
  const isWithin48Hours = Math.abs(diffInHours) <= 48

  if (isWithin48Hours) {
    const dateAdd48 = targetDate.add(48, 'hour')
    const formattedDate = dateAdd48.format('M月DD日')
    $alert({
      messageHtml: `<div>您的商品目前处于正常配送时效内，商家将于<span style="color:#FF780A;">${formattedDate}</span>前发货，请您耐心等待。</div>`,
      confirmButtonText: '确定',
      allowHtml: true,
      messageAlign: 'center'
    })
  } else {
    $alert({
      message: '给您带来的不便深感抱歉，已为您提醒商家发货，请您耐心等待。',
      confirmButtonText: '确定',
      messageAlign: 'center'
    })
  }
}

// 查看物流
const handleViewLogistics = async () => {
  const { orderDate } = orderInfo.value
  const now = dayjs()
  const orderDateDayjs = dayjs(orderDate)
  const endTimeSub180 = now.subtract(12, 'month')
  const isWithinScope = orderDateDayjs.isBefore(endTimeSub180, 'minute')

  if (isWithinScope) {
    showToast('物流信息已失效 ！')
    return
  }

  try {
    const [err, orderExpress] = await getOrderExpress(orderId.value)

    if (err) {
      showToast('查询物流信息失败')
      return
    }

    const packageList = get(orderExpress, 'orderPackageList', [])
    if (!isEmpty(packageList)) {
      router.push({
        name: 'user-order-entry-express',
        params: {
          orderExpress: orderExpress
        },
        query: {
          orderId: orderId.value
        }
      })
      return
    }

    showToast('物流信息已失效 ！')
  } catch (error) {
    console.error('查询物流信息失败:', error)
    showToast('查询物流信息失败')
  }
}

// 查看保证金详情
const handleViewDeposit = () => {
  // 这里可以添加跳转到保证金详情页面的逻辑
  const url = `/itf-fi-core-web/richer/assetDetail?orderNo=${orderInfo.value.bondOrderId}&orgCode=zbbank`
  // 跳转到目标页面
  window.location.href = url
}

// 获取商品项操作按钮
const getItemActions = (item) => {
  const actions = []
  const orderState = item.orderState
  const subOrder = item.subOrderRawData
  const afterSaleApplyList = get(subOrder, 'afterSaleApplyList', [])
  const isApplyAfterSales = afterSaleApplyList.length > 0
  const isExpired = !isExpires(item)
  const supplierCode = get(subOrder, 'skuNumInfoList[0].sku.supplierCode', '')
  const isJDSupplier = supplierCode.indexOf('jd_') > -1

  // 加购物车按钮 - 所有状态都显示
  actions.push({
    key: 'addToCart',
    label: '加购物车',
    type: 'default',
    handler: handleAddToCart
  })

  // 售后按钮逻辑
  if (afterSalesInfo.value.applyType) {
    // 有售后类型配置时的逻辑
    if (afterSalesInfo.value.applyType === '1') {
      // 申请退款
      if (isJDSupplier && (orderState === ORDER_STATE.PENDING_DELIVERY_1 || orderState === ORDER_STATE.PENDING_DELIVERY_3 || orderState === ORDER_STATE.SHIPPING) && isApplyAfterSales) {
        actions.push({
          key: 'applyRefund',
          label: '申请退款',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: (item) => handleAfterSalesAction(item, 0)
        })
      } else if (!isJDSupplier && (orderState === ORDER_STATE.PENDING_DELIVERY_1 || orderState === ORDER_STATE.PENDING_DELIVERY_3) && isApplyAfterSales) {
        actions.push({
          key: 'applyRefund',
          label: '申请退款',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: (item) => handleAfterSalesAction(item, 0)
        })
      }
    } else if (afterSalesInfo.value.applyType === '2') {
      // 申请售后
      if (isJDSupplier && orderState === ORDER_STATE.COMPLETED && isApplyAfterSales) {
        actions.push({
          key: 'applyAfterSale',
          label: '申请售后',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: (item) => handleAfterSalesAction(item, 1)
        })
      } else if (!isJDSupplier && (orderState === ORDER_STATE.SHIPPING || orderState === ORDER_STATE.COMPLETED) && isApplyAfterSales) {
        actions.push({
          key: 'applyAfterSale',
          label: '申请售后',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: (item) => handleAfterSalesAction(item, 1)
        })
      }
    }
  } else {
    // 没有售后类型配置时的默认逻辑
    if (isJDSupplier && (orderState === ORDER_STATE.PENDING_DELIVERY_1 || orderState === ORDER_STATE.PENDING_DELIVERY_3 || orderState === ORDER_STATE.SHIPPING) && !isApplyAfterSales) {
      actions.push({
        key: 'applyRefund',
        label: '申请退款',
        type: isExpired && isApplyAfterSales ? 'default' : 'gradient',
        disabled: isExpired,
        handler: (item) => handleAfterSalesAction(item, 0)
      })
    } else if (!isJDSupplier && (orderState === ORDER_STATE.PENDING_DELIVERY_1 || orderState === ORDER_STATE.PENDING_DELIVERY_3) && !isApplyAfterSales) {
      actions.push({
        key: 'applyRefund',
        label: '申请退款',
        type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
        disabled: isExpired,
        handler: (item) => handleAfterSalesAction(item, 0)
      })
    }

    if (isJDSupplier && orderState === ORDER_STATE.COMPLETED && !isApplyAfterSales) {
      actions.push({
        key: 'applyAfterSale',
        label: '申请售后',
        type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
        disabled: isExpired,
        handler: (item) => handleAfterSalesAction(item, 1)
      })
    } else if (!isJDSupplier && (orderState === ORDER_STATE.SHIPPING || orderState === ORDER_STATE.COMPLETED) && !isApplyAfterSales) {
      actions.push({
        key: 'applyAfterSale',
        label: '申请售后',
        type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
        disabled: isExpired,
        handler: (item) => handleAfterSalesAction(item, 1)
      })
    }

    // 查看详情按钮
    if (isApplyAfterSales) {
      actions.push({
        key: 'viewDetails',
        label: '查看详情',
        type: isExpired && isApplyAfterSales ? 'default' : 'gradient',
        disabled: isExpired,
        handler: (item) => handleAfterSalesAction(item, 2)
      })
    }
  }

  return actions
}

// 获取底部操作按钮
const getBottomActions = (orderState) => {
  const actions = []

  switch (orderState) {
    case ORDER_STATE.PENDING_PAYMENT:
      actions.push(
        {
          key: 'cancelOrder',
          label: '取消订单',
          type: 'text',
          handler: handleCancelOrder
        },
        {
          key: 'goToPay',
          label: '去支付',
          type: 'gradient',
          handler: handleGoToPay
        }
      )
      break

    case ORDER_STATE.CANCELLED:
      actions.push(
        {
          key: 'deleteOrder',
          label: '删除订单',
          type: 'secondary',
          handler: handleDeleteOrder
        },
        {
          key: 'buyAgain',
          label: '再次购买',
          type: 'gradient',
          handler: handleBuyAgain
        }
      )
      break

    case ORDER_STATE.PENDING_DELIVERY_1:
    case ORDER_STATE.PENDING_DELIVERY_3:
      actions.push(
        {
          key: 'buyAgain',
          label: '再次购买',
          type: 'secondary',
          handler: handleBuyAgain
        },
        {
          key: 'urgeDelivery',
          label: '催发货',
          type: 'gradient',
          handler: handleUrgeDelivery
        }
      )
      break

    case ORDER_STATE.SHIPPING:
      actions.push(
        {
          key: 'viewLogistics',
          label: '查看物流',
          type: 'secondary',
          handler: handleViewLogistics
        },
        {
          key: 'urgeDelivery',
          label: '催发货',
          type: 'gradient',
          handler: handleUrgeDelivery
        }
      )
      break

    case ORDER_STATE.COMPLETED:
      actions.push(
        {
          key: 'deleteOrder',
          label: '删除订单',
          type: 'secondary',
          handler: handleDeleteOrder
        },
        {
          key: 'viewLogistics',
          label: '查看物流',
          type: 'secondary',
          handler: handleViewLogistics
        },
        {
          key: 'urgeDelivery',
          label: '催发货',
          type: 'gradient',
          handler: handleUrgeDelivery
        }
      )
      break

    case ORDER_STATE.REFUNDED:
      actions.push(
        {
          key: 'deleteOrder',
          label: '删除订单',
          type: 'secondary',
          handler: handleDeleteOrder
        },
        {
          key: 'buyAgain',
          label: '再次购买',
          type: 'gradient',
          handler: handleBuyAgain
        }
      )
      break
  }

  return actions
}

// 组件挂载时启动倒计时和获取数据
onMounted(() => {
  getOrderInfoData()
})

// 组件卸载时清除倒计时
onUnmounted(() => {
  clearCountdown()
})
</script>

<style scoped lang="less">
.order-detail {
  min-height: 100vh;
  background-color: @bg-color-gray;

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;

    .loading-text {
      font-size: @font-size-14;
      color: @text-color-secondary;
    }
  }

  // 订单状态头部样式
  &__header {
    width: 100%;
    height: 70px;
    background: @bg-color-white;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  &__main {
    padding: 8px 10px;
    box-sizing: border-box;
  }

  // 订单状态样式
  .order-status {
    &__title {
      margin: 0;
      font-size: @font-size-20;
      font-weight: @font-weight-600;
      color: @text-color-primary;
      line-height: 1.5;
    }

    &__subtitle {
      font-size: @font-size-15;
      font-weight: @font-weight-400;
      line-height: 1.5;
      color: @text-color-primary;
    }

    &__time {
      color: @theme-color;
      font-weight: @font-weight-600;
    }
  }

  // 物流状态模块样式
  .logistics-section {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    overflow: hidden;
  }

  .logistics-timeline {
    flex-shrink: 0;
    position: relative;

    &__item {
      margin-top: 2px;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    &__dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      position: relative;
      z-index: 2;

      &--received {
        background: #FF780A;
      }

      &--pending {
        background: rgba(255, 120, 10, 0.50);
      }
    }

    &__line {
      width: 2px;
      background-color: #E5E5E5;
      transition: height 0.3s ease;
      z-index: 1;
      margin: 5px 0;
      opacity: 0.5;
      background: rgba(255, 120, 10, 1);
    }
  }

  .logistics-content {
    flex: 1;
    overflow: hidden;
  }

  .logistics-status {
    &__content {
      display: flex;
      justify-content: space-between;
    }

    &__header {
      font-size: @font-size-14;
      color: @text-color-primary;
      font-weight: @font-weight-500;
      line-height: 1.4;
      margin: 0;
      display: flex;
      align-items: center;
      overflow: hidden;
    }

    &__icon {
      width: 16px;
      height: 16px;
      margin-right: 6px;
    }

    &__text {
      margin-right: 5px;
      flex-shrink: 0;
      color: @theme-color;
      font-weight: @font-weight-600;
    }

    &__detail {
      flex: 1;
      .ellipsis();
    }

    &__arrow {
      flex-shrink: 0;
      width: 7px;
      height: 10px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .logistics-receiver {
    margin-top: 16px;

    &__info {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-size: @font-size-14;
      color: @text-color-primary;
      font-weight: @font-weight-500;
    }

    &__name {
      margin-right: 12px;
      .ellipsis();
    }

    &__address {
      font-size: @font-size-12;
      color: @text-color-secondary;
      font-weight: @font-weight-400;
      line-height: 1.5;
      .multi-ellipsis(2);
    }
  }

  .address-section {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    &__header {
      display: flex;
      justify-content: space-between;
      width: 100%;
      overflow: hidden;
    }

    &__content {
      width: 100%;
    }
  }

  .address-user {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: @font-size-16;
    color: @text-color-primary;
    font-weight: @font-weight-500;
    line-height: 1.5;
    margin-right: 8px;
    overflow: hidden;

    &__icon {
      width: 13px;
      height: 15px;
      margin-right: 8px;
    }

    &__name {
      margin-right: 12px;
      .ellipsis();
    }
  }

  .address-action {
    font-size: @font-size-14;
    font-weight: @font-weight-500;
    line-height: 1.5;

    &__button {
      display: flex;
      align-items: center;
      color: @theme-color;
    }

    &__icon {
      margin-right: 4px;
      width: 13px;
      height: 13px;
      vertical-align: middle;
    }
  }

  .address-detail {
    font-size: @font-size-12;
    color: @text-color-secondary;
    font-weight: @font-weight-400;
    line-height: 1.5;
    .multi-ellipsis(2);
  }

  .order-info-section {
    background-color: @bg-color-white;
    padding: 13px 15px;
    margin-bottom: 8px;
    border-radius: @radius-10;

    .order-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: @font-size-13;
        color: @text-color-secondary;
        line-height: 1.5;
      }

      .value {
        font-size: @font-size-13;
        color: @text-color-primary;
        line-height: 1.5;
      }
    }
  }

  // 支付单号 带复制
  .order-number {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;

    &__text {
      .ellipsis();
      max-width: calc(100% - 24px);
    }

    &__copy {
      flex-shrink: 0;
      margin-left: 8px;
      font-size: @font-size-11;
      color: @text-color-primary;
      background: #F6F6F6;
      border-radius: @radius-10;
      width: 38px;
      height: 18px;
      box-sizing: border-box;
      text-align: center;
      line-height: 18px
    }
  }

  // 收货地址信息
  .receiver-info {
    width: 100%;
    display: flex;
    flex-direction: column;
    line-height: 1.5;

    &__contact {
      font-size: @font-size-13;
      color: @text-color-primary;
    }

    &__address {
      flex: 1;
      font-size: @font-size-13;
      color: @text-color-primary;
      .ellipsis();
    }
  }

  // 保证金样式
  .deposit-section {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &__info {
      display: flex;
      align-items: center;
    }

    &__icon {
      width: 14px;
      height: 16px;
      margin-right: 8px;
    }

    &__label {
      font-size: @font-size-14;
      color: @text-color-primary;
      font-weight: @font-weight-500;
    }

    &__amount {
      flex: 1;
      text-align: center;
      font-size: @font-size-14;
      color: @text-color-primary;
    }

    &__action {
      display: flex;
      align-items: center;
    }
  }

  .deposit-amount {
    font-size: @font-size-14;
    color: @text-color-primary;
  }

  .deposit-action {
    &__text {
      font-size: @font-size-13;
      color: @text-color-primary;
      margin-right: 8px;
    }

    &__icon {
      width: 6px;
      height: 12px;
    }
  }

  .action-bar {
    width: 100%;
    display: flex;
    gap: 12px;
    justify-content: flex-end;

    &--pending-payment {
      justify-content: space-between;
    }
  }

  // 骨架屏样式
  &__skeleton {
    padding: 0 15px;
  }

  &__skeleton-header {
    background-color: @bg-color-white;
    padding: 20px 15px;
    margin: 0 -15px 15px -15px;
    border-radius: 0 0 10px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    .skeleton-line--title {
      width: 120px;
      height: 24px;
      margin-bottom: 8px;
    }

    .skeleton-line--subtitle {
      width: 200px;
      height: 16px;
    }
  }

  &__skeleton-content {
    .skeleton-card {
      background-color: @bg-color-white;
      border-radius: @radius-10;
      padding: 15px;
      margin-bottom: 8px;
    }

    .skeleton-logistics {
      display: flex;

      &__timeline {
        width: 30px;
        margin-right: 15px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .skeleton-circle {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: #f0f0f0;
        }

        .skeleton-line--vertical {
          width: 2px;
          height: 25px;
          background-color: #f0f0f0;
          margin: 5px 0;
        }
      }

      &__content {
        flex: 1;

        .skeleton-line--logistics-title {
          width: 80%;
          height: 18px;
          margin-bottom: 8px;
        }

        .skeleton-line--logistics-detail {
          width: 90%;
          height: 14px;
          margin-bottom: 12px;
        }

        .skeleton-line--logistics-address {
          width: 70%;
          height: 14px;
        }
      }
    }

    .skeleton-address {
      &__header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        .skeleton-line--address-user {
          width: 150px;
          height: 18px;
        }

        .skeleton-line--address-action {
          width: 60px;
          height: 18px;
        }
      }

      .skeleton-line--address-detail {
        width: 85%;
        height: 14px;
      }
    }

    .skeleton-goods {
      display: flex;

      &__image {
        width: 90px;
        height: 90px;
        border-radius: @radius-10;
        background-color: #f0f0f0;
        margin-right: 12px;
        flex-shrink: 0;
      }

      &__info {
        flex: 1;

        .skeleton-line--goods-title {
          width: 90%;
          height: 16px;
          margin-bottom: 8px;
        }

        .skeleton-line--goods-spec {
          width: 60%;
          height: 14px;
          margin-bottom: 8px;
        }

        .skeleton-line--goods-price {
          width: 80px;
          height: 16px;
        }
      }
    }

    .skeleton-price {
      .skeleton-line--price-row {
        width: 100%;
        height: 16px;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .skeleton-line--price-total {
        width: 100%;
        height: 20px;
        margin-top: 8px;
      }
    }

    .skeleton-order {
      .skeleton-line--info-row {
        width: 100%;
        height: 16px;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .skeleton-actions {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: @bg-color-white;
      padding: 12px 15px;
      display: flex;
      gap: 12px;
      justify-content: flex-end;

      .skeleton-button {
        width: 80px;
        height: 36px;
        border-radius: @radius-18;
        background-color: #f0f0f0;

        &--primary {
          width: 120px;
          background-color: #e6f7ff;
        }
      }
    }
  }

  // 骨架屏动画
  .skeleton-line,
  .skeleton-circle,
  .skeleton-goods__image,
  .skeleton-button {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
  }

  @keyframes skeleton-loading {
    0% {
      background-position: 200% 0;
    }

    100% {
      background-position: -200% 0;
    }
  }
}
</style>
