<template>
  <div class="order-list">
    <!-- 顶部搜索区域 -->
    <SearchHeader v-model="searchKeyword" placeholder="搜索我的订单" :redirectToSearch="true" redirectUrl="/user/order/search"
      @search="handleSearch">
      <!-- 自定义右侧操作区插槽 -->
      <template #right-action>
        <div class="recycle-icon" @click="goToRecycle">
          <img src="@/static/images/recycle.png" alt="回收站" />
        </div>
      </template>
    </SearchHeader>


    <!-- Tab切换区域 -->
    <van-tabs v-model:active="activeTab" sticky @change="handleTabChange">
      <van-tab v-for="(tab, index) in orderTabs" :key="tab.key" :title="tab.name">
        <!-- 下拉刷新包装器 -->
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <OrderTabContent v-if="activeTab === index" :ref="el => tabContentRefs[index] = el"
            :key="`${tab.key}-${tabChangeKey}`" :tab-type="tab.key" :scroll-position="scrollPositions[index]"
            @update-scroll="updateScrollPosition(index, $event)" />
        </van-pull-refresh>
      </van-tab>
    </van-tabs>

  </div>
</template>

<script setup>
import { ref, onActivated, onDeactivated, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import OrderTabContent from '../components/OrderTabContent.vue'
import SearchHeader from "@components/Common/SearchHeader.vue";

// 路由实例
const router = useRouter()
const route = useRoute()

// 搜索关键词
const searchKeyword = ref('')

// 下拉刷新状态
const refreshing = ref(false)

// Tab内容组件引用
const tabContentRefs = ref([])

// 订单标签配置
const orderTabs = [
  { name: '全部', key: '' },
  { name: '待付款', key: '0' },
  { name: '待发货', key: '3' },
  { name: '待收货', key: '5' },
  { name: '已完成', key: '9' }
]

// 当前激活的标签页
const activeTab = ref(0)

// Tab切换计数器，用于强制重新创建组件
const tabChangeKey = ref(0)

// 根据路由参数type设置默认激活的标签页
const setActiveTabByType = () => {
  const type = route.params.type || route.query.type
  if (type) {
    const tabIndex = orderTabs.findIndex(tab => tab.key === type)
    if (tabIndex !== -1) {
      activeTab.value = tabIndex
    }
  }
}

// 保存每个标签页的滚动位置
const scrollPositions = ref(
  orderTabs.reduce((acc, tab, index) => {
    acc[index] = 0
    return acc
  }, {})
)

// 更新滚动位置
const updateScrollPosition = (tabIndex, position) => {
  scrollPositions.value[tabIndex] = position
}

// 标签切换处理函数
const handleTabChange = (index) => {
  console.log('切换到标签:', index)
  const currentTab = orderTabs[index]

  // 增加切换计数器，强制重新创建当前tab的组件
  tabChangeKey.value++

  // 构建新的查询参数
  const newQuery = {
    ...route.query, // 保留现有的查询参数如 _t 和 distri_biz_code
    type: currentTab.key || undefined // 如果是全部标签，设置为undefined会自动移除该参数
  }

  // 如果是全部标签，移除type参数
  if (!currentTab.key) {
    delete newQuery.type
  }

  // 更新路由参数，保持原有路径和其他查询参数
  router.replace({
    path: route.path,
    query: newQuery
  }, () => { }, { shallow: true })
}

// 搜索处理函数
const handleSearch = () => {
  // 实现搜索逻辑
  console.log('搜索关键词:', searchKeyword.value)
}

// 前往回收站
const goToRecycle = () => {
  // 实现跳转到回收站页面的逻辑
  router.push('/user/order/recycle')
  console.log('前往回收站')
}

// 下拉刷新处理
const onRefresh = async () => {
  try {
    // 获取当前激活的tab内容组件
    const currentTabContent = tabContentRefs.value[activeTab.value]

    if (currentTabContent && typeof currentTabContent.refreshData === 'function') {
      // 调用当前tab的刷新方法
      await currentTabContent.refreshData()
    }
  } catch (error) {
    console.error('刷新失败:', error)
  } finally {
    // 结束刷新状态
    refreshing.value = false
  }
}

// 组件挂载时设置默认标签页
onMounted(() => {
  setActiveTabByType()
})

// 页面激活时触发
onActivated(() => {
  // 页面重新显示时，可以在这里恢复滚动位置
  console.log('页面激活')
})

// 页面失活时触发
onDeactivated(() => {
  // 页面隐藏时，可以在这里保存一些状态
  console.log('页面失活')
})
</script>

<style scoped lang="less">
.order-list {
  background-color: #f5f5f5;
  height: 100vh;
  overflow: hidden;
}

:deep(.van-pull-refresh) {
  height: calc(100vh - 44px); // 减去搜索头部高度
  overflow: auto;
}

.recycle-icon {
  margin-left: 12px;
  padding: 4px;

  img {
    width: 20px;
    height: 20px;
  }
}

:deep(.van-tab) {
  height: 42px;
}

:deep(.van-tabs__line) {
  background-color: @theme-color;
}

:deep(.van-tab--active) {
  color: @theme-color;
  font-weight: bold;
}

:deep(.van-tabs__nav) {
  background-color: #fff;
}
</style>
