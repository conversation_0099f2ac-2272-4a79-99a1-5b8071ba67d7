<template>
  <div class="wish-page">
    <section class="wish-banner">
      <img class="wish-banner-img" @click="goBannerDetail" src="./assets/wishimgOp.jpg" alt="图片加载失败">
      <img class="wish-my" @click="onWishListDetailPage" src="./assets/wishimgMy.png" alt="图片加载失败">
    </section>
    <main class="wish-content">
      <p class="wish-content-tips">由于近期心愿单需求量大，审核时间较长，请耐心等待!</p>
      <Form validate-first scroll-to-error :show-error="false" class="wish-form" ref="wishFormRef" @submit="onSubmit"
        @failed="onFailed">
        <!-- 通过 pattern 进行正则校验 -->
        <Field label="适用商城" name="mallName" placeholder="适用商城" required>
          <template #input>
            {{ mallName }}
          </template>
        </Field>
        <!-- 通过 validator 进行函数校验 -->
        <Field label="商品编码" v-model="wishListForm.supplierGoodsId" name="supplierGoodsId" type="number"
          placeholder="商品编码" required clearable :rules="[{ required: true, message: '请填写商品编码' },
           { pattern:/^(1000\d*|1001\d*|1002\d*|(?!1000|1001|1002)[0-9]{6,8})$/, message: '请输入正确商品编码' }]"
        >
          <template #right-icon>
            <Icon name="info-o" @click="onTips" />
          </template>
        </Field>
        <!-- 通过 validator 进行异步函数校验 -->
        <Field label="商品名称" v-model="wishListForm.supplierGoodsName" name="supplierGoodsName" placeholder="商品名称"
          maxlength="100" required clearable :rules="[{ required: true, message: '请填写商品名称' }]" />
        <Field label="商品链接" v-model="wishListForm.supplierGoodsLink" name="supplierGoodsLink" placeholder="商品链接"
          clearable />
        <Field label="联系人" v-model="wishListForm.contactor" name="contactor" clearable placeholder="联系人" />
        <Field label="联系电话" v-model="wishListForm.contactMobile" name="contactMobile" type="tel" clearable
          maxlength="11" placeholder="联系电话" />
        <Field label="备注" v-model="wishListForm.remark" name="remark" rows="1" type="textarea" maxlength="100"
          placeholder="请填写商品需求，如商品收货地区，数量等" show-word-limit clearable />
        <div class="kind-reminder">
          <p class="kind-reminder-tip"> <span class="kind-reminder-starmark">*</span> 温馨提示： </p>
          <p class="kind-reminder-tip">1. 可上架商品分类：<span class="kind-reminder-link"
              @click="goWishCategory">点击查看</span>，非列表中的分类不能上架。</p>
          <p class="kind-reminder-tip">2. 商品编码分为二类：一类以1000、1001、1002开头，二类为6到8位编码。 </p>
          <p class="kind-reminder-tip">3. 已上架商品如显示所在地区无货的，在提交申请后请耐心等待供应商补货。 </p>
          <p class="kind-reminder-tip">4. 商品编码填错会影响上架时效，请确保填写正确。 </p>
          <p class="kind-reminder-tip">5. 如商品审核拒绝则表示不符合品类要求，请勿重复提交。 </p>
          <p class="kind-reminder-tip">6. 增补商品需是福利费适用商品，超范围商品不能上架。</p>
          <p class="kind-reminder-tip">7. 审核周期为3个工作日，请耐心等待。</p>
        </div>
      </Form>
    </main>
    <div class="action-bar-container">
      <WoActionBarPlaceholder />
      <WoActionBar>
        <WoButton type="gradient" size="xlarge" block native-type="submit" @click="onSubmit">提交</WoButton>
      </WoActionBar>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { showToast, showLoadingToast, closeToast, Form, Field, Icon, } from 'vant'
import { getBizCode } from '@/utils/curEnv'
import { submitWishInfo } from '@/api/interface/wish'
import { isUnicom, isWopay } from 'commonkit'
import { useRouter } from 'vue-router'
import { useAlert } from '@/hooks/index.js'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
const showAlert = useAlert()
const router = useRouter()
const wishFormRef = ref(null)

const wishListForm = ref({
  supplierGoodsId: '',
  supplierGoodsName: '',
  supplierGoodsLink: '',
  remark: '',
  contactor: '',
  contactMobile: ''
})

const mallName = computed(() => {
  const bizCode = getBizCode()
  switch (bizCode) {
    case 'ziying':
    case 'welfaresop':
      return '沃百富'
    case 'labor':
      return '劳保商城'
    case 'ygjd':
      return '京东商城'
    default:
      // 不在computed中处理副作用，而是在组件挂载时检查
      return '未知商城'
  }
})

const goBannerDetail = () => {
  // 操作手册根据彩虹模版来
  if (isWopay) {
    window.location.href = '/ci-mcss-party-web/rainbow/?templateName=XYDSC&bizFrom=212&bizChannelCode=212'
  } else if (isUnicom) {
    window.location.href = '/ci-mcss-party-web/rainbow/?templateName=XYDSC&bizFrom=225&bizChannelCode=225'
  } else {
    window.location.href = '/ci-mcss-party-web/rainbow/?templateName=XYDSC&bizFrom=226&bizChannelCode=226'
  }
}

const goWishCategory = () => {
  router.push('/user/wish/category')
}

const onFailed = (errorInfo) => {
  console.warn('failed', errorInfo)
}

const onSubmit = async (value) => {
  const params = {
    bizCode: getBizCode(),
    ...wishListForm.value
  }
  showLoadingToast()
  const [err] = await submitWishInfo(params)
  closeToast()
  if (!err) {
    wishListForm.value = {
      supplierGoodsId: '',
      supplierGoodsName: '',
      supplierGoodsLink: '',
      remark: '',
      contactor: '',
      contactMobile: ''
    }
    showAlert({
      title: '',
      message: '审核中，请耐心等待~',
      showCancelButton: true,
      confirmButtonText: '查看申请记录',
      cancelButtonText: '返回',
      onConfirmCallback: () => {
        router.push({
          path: '/user/wish/detail'
        })
      },
    });
  } else {
    showToast(err.msg)
  }
}

const onTips = () => {
  showAlert({
    title: '商品编码详情',
    message: '商品编码分为二类：一类以1000、1001、1002开头，二类为6到8位编码。',
    showCancelButton: false,
  });
}

const onWishListDetailPage = () => {
  router.push({
    path: '/user/wish/list'
  })
}

// 添加组件挂载时的检查
onMounted(() => {
  const bizCode = getBizCode()
  if (!['ziying', 'welfaresop', 'labor', 'ygjd'].includes(bizCode)) {
    showAlert({
      title: '非法访问',
      message: '非法访问，请访问正确渠道。',
      showCancelButton: false,
      onConfirmCallback: () => {
        router.go(-1)
      }
    }).catch(() => {
      // 处理用户取消的情况
      console.log('用户取消了对话框')
    })
  }
})
</script>

<style scoped lang="less">
.wish-page {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-direction: column;
  width: 100vw;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.wish-my {
  position: absolute;
  top: 5px;
  right: 5px;
  height: 28px;
  width: auto;
}

.wish-banner {
  position: relative;
  width: 100%;
  .wish-banner-img {
    display: block;
    width: 100%;
    height: 190px;
  }
}

.wish-content {
  flex: 1;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 10px;
  .wish-content-tips {
    font-size: @font-size-12;
    color: @color-red;
    margin-bottom: @padding-page;
  }
}

.wish-form {
  font-size: @font-size-12;
  background-color: @bg-color-white;
}

.kind-reminder {
  padding: 5px;
  .kind-reminder-tip {
    color: #999999;
    line-height: 1.5;
    text-align: justify;
  }

  .kind-reminder-starmark {
    color: @color-red;
  }

  .kind-reminder-link {
    color: #4d97f0;
  }

  .kind-reminder-tip--intent {
    text-indent: 2em;
  }
}
</style>
