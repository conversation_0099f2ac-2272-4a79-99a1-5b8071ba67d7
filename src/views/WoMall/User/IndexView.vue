<template>
  <MainLayout>
    <div class="user-page">
      <header class="user-page__header">
        <h2 class="user-page__title">我的订单</h2>
        <nav class="order-status" role="navigation" aria-label="订单状态导航">
          <button v-for="item in bannerList" :key="item.key" class="order-status__item"
            :class="{ 'order-status__item--separator': item.separator }"
            :aria-label="item.separator ? '分隔符' : `${item.name}订单`" @click="handleOrderClick(item.key)">
            <!-- 分隔符 -->
            <i v-if="item.separator" :class="['order-status__separator', item.icon]" aria-hidden="true" />
            <!-- 订单状态 -->
            <div v-else class="order-status__content">
              <van-badge :content="orderCounts[item.key] || 0" :show-zero="false" :max="99" class="order-status__badge">
                <i :class="['order-status__icon', item.icon]" aria-hidden="true" />
              </van-badge>
              <span class="order-status__text">{{ item.name }}</span>
            </div>
          </button>
        </nav>
      </header>

      <div class="user-page__divider" />

      <section class="user-links">
        <button v-for="link in visibleLinks" :key="link.key" class="user-links__item" :aria-label="`进入${link.text}`"
          @click="link.handler">
          <span class="user-links__text">{{ link.text }}</span>
          <i class="user-links__arrow" aria-hidden="true" />
        </button>
      </section>
    </div>
  </MainLayout>
</template>

<script setup>
import { computed, onMounted, onUnmounted, shallowRef } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { throttle } from 'lodash-es'
import { getBizCode } from '@/utils/curEnv'
import { useUserStore } from '@/store/modules/user'
import { useOrderStore } from '@/store/modules/order'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'
import { closeToast, showLoadingToast } from 'vant'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const orderStore = useOrderStore()

// 订单状态列表 - 使用 shallowRef 避免深度响应
const bannerList = shallowRef([
  { name: '待付款', icon: 'icon-dfk', key: '0' },
  { name: '待发货', icon: 'icon-dfh', key: '3' },
  { name: '待收货', icon: 'icon-dsh', key: '5' },
  { name: '售后', icon: 'icon-sh', key: '-1' },
  { separator: true, icon: 'icon-separator', key: '999' },
  { name: '所有订单', icon: 'icon-sydd', key: '' }
])

// 缓存 bizCode 避免重复计算
const currentBizCode = getBizCode()
const isWishShow = ['ziying', 'labor', 'ygjd'].includes(currentBizCode)

// 订单数量缓存
const orderCounts = computed(() => {
  const counts = {}
  bannerList.value.forEach(item => {
    if (!item.separator) {
      counts[item.key] = orderStore.getCountByType(item.key)
    }
  })
  return counts
})

// 链接配置 - 预计算显示状态
const linkConfigs = shallowRef([
  {
    key: 'addr',
    text: '地址管理',
    handler: () => router.push({ path: '/addr/list', query: { callback: route.path } }),
    show: true
  },
  {
    key: 'afterSale',
    text: '售后服务',
    handler: () => router.push({
      path: '/wo-after-sales',
      query: { type: '-1', _t: Date.now().toString() }
    }),
    show: true
  },
  {
    key: 'help',
    text: '帮助中心',
    handler: () => router.push('/user/help'),
    show: currentBizCode === 'fupin'
  },
  {
    key: 'hotline',
    text: '在线客服',
    handler: () => {
      window.location.href = 'https://service.unicompayment.com/live800/chatClient/chatbox.jsp?companyID=9061&configID=47&pagereferrer=%e5%95%86%e5%9f%8e&chatfrom=sc&enterurl=sc&sc=sc'
    },
    show: true
  },
  {
    key: 'wish',
    text: '心愿单',
    handler: () => router.push({
      name: 'user-wish',
      query: { ...route.query, timestamp: Date.now() }
    }),
    show: isWishShow
  }
])

// 可见链接 - 预过滤
const visibleLinks = linkConfigs.value.filter(link => link.show)

// 节流处理订单点击
const handleOrderClick = throttle((type) => {
  if (type === '-1') {
    router.push({
      path: '/wo-after-sales',
      query: { type, _t: Date.now().toString() }
    })
  } else {
    router.push({
      path: '/user/order/list',
      query: { type, _t: Date.now().toString() }
    })
  }
}, 300)

// 检查登录并获取订单数据 - 优化加载策略
const checkLoginAndFetchData = async () => {
  try {
    await userStore.queryLoginStatus()
    if (userStore.isLogin) {
      showLoadingToast()
      await orderStore.fetchOrderCountDebounced()
      closeToast()
    }
  } catch (error) {
    // console.error('初始化数据失败:', error)
  }
}

onMounted(() => {
  checkLoginAndFetchData()
})

onUnmounted(() => {
  orderStore.clearCache()
})
</script>

<style lang="less" scoped>
.user-page {
  width: 100%;
  background: @bg-color-white;
  contain: layout style; // CSS containment 优化

  &__header {
    padding: 15px 17px 14px;
    contain: layout; // 局部 containment
  }

  &__title {
    margin-bottom: 10px;
    text-align: left;
    font-size: @font-size-16;
    font-weight: @font-weight-700;
    color: @text-color-primary;
    contain: style; // 样式隔离
  }

  &__divider {
    width: 100%;
    height: 10px;
    background: @bg-color-gray;
    margin: 0;
    will-change: auto; // 避免不必要的合成层
  }
}

.order-status {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex-wrap: nowrap;
  text-align: center;
  contain: layout; // 布局优化

  &__item {
    position: relative;
    width: 19%;
    border: none;
    background: transparent;
    padding: 0;
    cursor: pointer;
    touch-action: manipulation; // 优化触摸响应
    -webkit-tap-highlight-color: transparent; // 移除点击高亮

    &--separator {
      width: 5%;
    }

    // 添加点击反馈
    &:active:not(&--separator) {
      transform: scale(0.95);
      transition: transform 0.1s ease;
    }
  }

  &__separator {
    display: block;
    margin: 0 auto;
    width: 6px;
    height: 33px;
    background-size: 100%;
    background-repeat: no-repeat;

    &.icon-separator {
      background-image: url(./assets/icon-status-separator.png);
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__icon {
    display: block;
    margin: 0 auto;
    width: 33px;
    height: 33px;
    background-size: 100%;
    background-repeat: no-repeat;
    will-change: transform; // 优化动画性能
    transform: translateZ(0); // 启用硬件加速

    &.icon-dfh {
      background-image: url(./assets/icon-status-dfh.png);
    }

    &.icon-dfk {
      background-image: url(./assets/icon-status-dfk.png);
    }

    &.icon-dsh {
      background-image: url(./assets/icon-status-dsh.png);
    }

    &.icon-ywc {
      background-image: url(./assets/icon-status-ywc.png);
    }

    &.icon-sh {
      background-image: url(./assets/icon-status-sh.png);
    }

    &.icon-sydd {
      background-image: url(./assets/icon-status-sydd.png);
    }
  }

  &__badge {
    position: relative;
    display: inline-block;

    :deep(.van-badge) {
      background: @theme-color;

      .van-badge__wrapper {
        position: static;
      }

      .van-badge__content {
        position: absolute;
        top: -6px;
        right: -6px;
        min-width: 16px;
        height: 16px;
        padding: 0 4px;
        background: @theme-color;
        border: 1px solid @bg-color-white;
        border-radius: @radius-8;
        font-size: @font-size-11;
        line-height: 14px;
        color: @text-color-white;
        text-align: center;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        transform: scale(0.9);
        transform-origin: center;
      }
    }
  }

  &__text {
    margin-top: 5px;
    margin-bottom: 0;
    font-size: @font-size-12;
    color: @text-color-secondary;
  }
}

.user-links {
  display: flex;
  flex-direction: column;
  padding: 0 17px;
  box-sizing: border-box;
  contain: layout; // 布局优化

  &__item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    width: 100%;
    height: 55px;
    border: none;
    border-bottom: 1px solid @divider-color-base;
    background: transparent;
    box-sizing: border-box;
    cursor: pointer;
    text-align: left;
    touch-action: manipulation; // 优化触摸响应
    -webkit-tap-highlight-color: transparent; // 移除点击高亮

    // 添加点击反馈
    &:active {
      background-color: rgba(0, 0, 0, 0.05);
      transition: background-color 0.1s ease;
    }
  }

  &__text {
    font-size: @font-size-17;
    color: @text-color-primary;
    contain: style; // 样式隔离
  }

  &__arrow {
    width: 17px;
    height: 17px;
    background-size: 100%;
    background-repeat: no-repeat;
    background-image: url(./assets/next-step.png);
    will-change: transform; // 优化动画性能
    transform: translateZ(0); // 启用硬件加速
  }
}
</style>
