<template>
  <div class="goods-list-page">
    <!-- 顶部搜索区域 -->
    <header class="page-header">
      <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirectToSearch="true" redirectUrl="/search"
        @search="handleSearch" @clickable="handleSearchClick">
        <template #right-action>
          <button class="layout-toggle" @click="toggleLayout" type="button">
            <img :src="isWaterfallLayout ? switchLayout2Img : switchLayoutImg" alt="切换布局" width="20" height="20" />
          </button>
        </template>
      </SearchHeader>

      <!-- 排序筛选区域 -->
      <SortFilterBar :sort-type="sortType" :sort-order="sortOrder" :has-filter-conditions="hasFilterConditions"
        @sort-change="handleSortChange" @filter-toggle="toggleFilter" />
    </header>

    <!-- 商品列表区域 -->
    <main class="goods-content">

      <!-- 骨架屏加载状态 -->
      <div v-if="isLoading" class="skeleton-container">
        <!-- 列表布局骨架屏 -->
        <div v-if="!isWaterfallLayout" class="list-skeleton">
          <div v-for="i in 3" :key="i" class="skeleton-item">
            <div class="skeleton-image">
              <div class="skeleton-block"></div>
            </div>
            <div class="skeleton-info">
              <div class="skeleton-info-main">
                <div class="skeleton-title"></div>
                <div class="skeleton-spec"></div>
              </div>
              <div class="skeleton-info-footer">
                <div class="skeleton-price"></div>
                <div class="skeleton-cart">
                  <div class="skeleton-block"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 瀑布流布局骨架屏 -->
        <div v-else class="waterfall-skeleton">
          <div class="skeleton-grid">
            <div v-for="i in 4" :key="i" class="skeleton-item">
              <div class="skeleton-image">
                <div class="skeleton-block"></div>
              </div>
              <div class="skeleton-content">
                <div class="skeleton-title"></div>
                <div class="skeleton-spec"></div>
                <div class="skeleton-sales"></div>
                <div class="skeleton-footer">
                  <div class="skeleton-price"></div>
                  <div class="skeleton-cart">
                    <div class="skeleton-block"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


      <!-- 普通列表布局 -->
      <section v-show="goodsList.length > 0 && !isWaterfallLayout" class="list-layout">
        <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <ul class="goods-list-container">
            <li v-for="(item, index) in goodsList" :key="`goods-${item.skuId || index}`" class="goods-item"
              @click="goToDetail(item)">
              <div class="goods-image">
                <img :src="item.showImageUrl" :alt="item.name" loading="lazy" width="85" height="85" />
              </div>
              <div class="goods-info">
                <div class="goods-info-main">
                  <h3 class="goods-title">{{ item.name }}</h3>
                  <p class="goods-spec">{{ item.params.join(' ') }}</p>
                </div>
                <div class="goods-info-footer">
                  <div class="price-sales">
                    <PriceDisplay :price="item.price" size="small" color="orange" />
                    <span class="sales-count">销量{{ item.realSaleVolume }}件</span>
                  </div>
                  <button class="cart-btn" @click.stop="addOneCart(item)" type="button" aria-label="加入购物车">
                    <img src="@/static/images/quick-cart.png" alt="" width="25" height="25" />
                  </button>
                </div>
              </div>
            </li>
          </ul>
        </van-list>
      </section>

      <!-- 瀑布流布局 -->
      <section v-show="goodsList.length > 0 && isWaterfallLayout" class="waterfall-layout">
        <van-list v-model:loading="loading" :finished="finished" finished-text="没有更多了" @load="onLoad">
          <Waterfall :list="goodsList" :breakpoints="breakpoints" :hasAroundGutter="false">
            <template #default="{ item }">
              <article class="waterfall-item" @click="goToDetail(item)">
                <div class="waterfall-card">
                  <div class="waterfall-image">
                    <img :src="item.showImageUrl" :alt="item.name" loading="lazy" />
                  </div>
                  <div class="waterfall-content">
                    <h3 class="waterfall-title">{{ item.name }}</h3>
                    <p class="waterfall-spec">{{ item.params.join(' ') }}</p>
                    <p class="waterfall-sales">销量{{ item.realSaleVolume }}件</p>
                    <div class="waterfall-footer">
                      <PriceDisplay :price="item.price" size="small" color="orange" />
                      <button class="cart-btn" @click.stop="addOneCart(item)" type="button" aria-label="加入购物车">
                        <img src="@/static/images/quick-cart.png" alt="" width="25" height="25" />
                      </button>
                    </div>
                  </div>
                </div>
              </article>
            </template>
          </Waterfall>
        </van-list>
      </section>

      <!-- 空状态 -->
      <section v-if="goodsList.length <= 0 && !isLoading" class="empty-state">
        <WoEmpty description="本地区无货" />
      </section>
    </main>

    <!-- 悬浮按钮 -->
    <FloatingBubble :offset="floatingBubbleOffset" @go-to-cart="goToCart" />

    <!-- 筛选弹窗 -->
    <FilterPopup v-model:show="isPopupShow" v-model="filterCriteria" :location-text="locationText"
      :category-id="categoryId" @switch-address="setSwitchAddressPopupShow" @confirm="handleFilterConfirm"
      @reset="handleFilterReset" />

    <!-- 地址切换弹窗 -->
    <AddressSwitchPopup v-model:show="isSwitchAddressPopupShow" @address-changed="handleAddressChanged" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { pick, compact, filter, debounce } from 'lodash-es'
import SearchHeader from '@components/Common/SearchHeader.vue'
import { useRoute, useRouter } from 'vue-router'
import WoEmpty from '@/components/WoElementCom/WoEmpty.vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import { getBizCode } from '@utils/curEnv.js'
import { skuPageList, addOneClick } from '@/api/index.js'
import { closeToast, showLoadingToast, showToast } from 'vant'
import switchLayoutImg from '@/static/images/switch-layout.png'
import switchLayout2Img from '@/static/images/switch-layout2.png'
import { useUserStore } from '@/store/modules/user.js'
import { useNewCartStore } from '@/store/modules/newCart.js'
import AddressSwitchPopup from '@/components/FilterTools/AddressSwitchPopup.vue'
import FilterPopup from '@/components/FilterTools/FilterPopup.vue'
import FloatingBubble from '@/components/FilterTools/FloatingBubble.vue'
import SortFilterBar from '@/components/FilterTools/SortFilterBar.vue'

const userStore = useUserStore()
const cartStore = useNewCartStore()
const router = useRouter()
const route = useRoute()

// 基础状态
const floatingBubbleOffset = ref({ bottom: 150 })
const searchKeyword = ref('')
const sortType = ref('sort')
const sortOrder = ref('desc')
const goodsList = ref([])
const loading = ref(false)
const finished = ref(false)
const isLoading = ref(true)
const pageNo = ref(1)
const pageSize = ref(10)
const categoryId = ref('')

// 布局切换和弹窗状态
const isWaterfallLayout = ref(false)
const isPopupShow = ref(false)
const isSwitchAddressPopupShow = ref(false)

// 切换方法
const toggleLayout = () => {
  isWaterfallLayout.value = !isWaterfallLayout.value
}

const toggleFilter = () => {
  isPopupShow.value = !isPopupShow.value
}

const toggleAddressPopup = (value) => {
  if (typeof value === 'boolean') {
    isSwitchAddressPopupShow.value = value
  } else {
    isSwitchAddressPopupShow.value = !isSwitchAddressPopupShow.value
  }
}

// 筛选条件
const filterCriteria = ref({
  isStock: false,
  minPrice: '',
  maxPrice: '',
  brandsList: []
})

// 瀑布流配置
const breakpoints = ref({
  750: { rowPerView: 2 },
  550: { rowPerView: 2 },
  375: { rowPerView: 2 },
  290: { rowPerView: 1 }
})

// 计算属性
const hasFilterConditions = computed(() => {
  const { isStock, minPrice, maxPrice, brandsList } = filterCriteria.value
  const hasStockFilter = isStock
  const hasPriceFilter = minPrice !== '' || maxPrice !== ''
  const hasBrandFilter = brandsList.some(brand => brand.isSelected)
  return hasStockFilter || hasPriceFilter || hasBrandFilter
})

const curAddrInfo = computed(() => userStore.curAddressInfo)

const locationText = computed(() => {
  const info = curAddrInfo.value
  return info.addrDetail || ''
})

// 地址信息提取
const addressInfo = computed(() => {
  const addressFields = ['provinceId', 'provinceName', 'cityId', 'cityName', 'countyId', 'countyName', 'townId', 'townName']
  return JSON.stringify(pick(curAddrInfo.value, addressFields))
})

// 方法
const handleSearch = () => {
  // pageNo.value = 1
  // finished.value = false
  // goodsList.value = []
  // fetchGoodsList()
}

const handleSearchClick = () => {
  console.log('搜索框被点击，即将跳转到搜索页面')
}

const handleSortChange = ({ type, currentSortType, currentSortOrder }) => {
  if (currentSortType === type) {
    if (type === 'price' || type === 'sale') {
      sortOrder.value = currentSortOrder === 'asc' ? 'desc' : 'asc'
    }
  } else {
    sortType.value = type
    sortOrder.value = ''
  }

  resetListAndFetch()
}

const setSwitchAddressPopupShow = () => {
  toggleAddressPopup(true)
}

// 重置列表并重新获取数据的通用方法
const resetListAndFetch = () => {
  pageNo.value = 1
  finished.value = false
  goodsList.value = []
  fetchGoodsList()
}

const handleFilterConfirm = (criteria) => {
  console.log('应用筛选条件:', criteria)
  resetListAndFetch()
}

const handleFilterReset = () => {
  console.log('重置筛选条件')
}

const handleAddressChanged = () => {
  resetListAndFetch()
}

// 获取商品列表
const fetchGoodsList = async () => {
  if (pageNo.value === 1) {
    isLoading.value = true
  }

  const brandList = filter(filterCriteria.value.brandsList, 'isSelected').map(item => item.value)

  showLoadingToast()
  const [err, json] = await skuPageList({
    bizCode: getBizCode('GOODS'),
    categoryId: categoryId.value,
    pageNo: pageNo.value,
    pageSize: pageSize.value,
    type: sortType.value,
    sort: sortOrder.value,
    brandList: JSON.stringify(brandList),
    priceFrom: filterCriteria.value.minPrice !== '' ? Number(filterCriteria.value.minPrice * 100) : '',
    priceTo: filterCriteria.value.maxPrice !== '' ? Number(filterCriteria.value.maxPrice * 100) : '',
    addressInfo: addressInfo.value
  })
  closeToast()

  if (pageNo.value === 1) {
    goodsList.value = []
  }

  loading.value = false
  isLoading.value = false

  if (!err) {
    if (json && json.skuList && json.skuList.length > 0) {
      const tmpList = json.skuList || []

      // 参数处理
      tmpList.forEach((item) => {
        if (item.listImageUrl) {
          item.showImageUrl = item.listImageUrl
        }
        // 使用 compact 过滤掉空值
        item.params = compact([item.param, item.param1, item.param2, item.param3, item.param4])
      })

      // 库存筛选处理
      if (filterCriteria.value.isStock) {
        const tmpListFilter = tmpList.filter(item => item.stock > 0)
        if (tmpListFilter.length <= 0 && json.cacheType === '1') {
          pageNo.value++
          fetchGoodsList()
          return
        }
        goodsList.value = goodsList.value.concat(tmpListFilter)
      } else {
        goodsList.value = goodsList.value.concat(tmpList)
      }

      if (json.cacheType === '1') {
        pageNo.value++
      } else {
        finished.value = true
      }
    } else {
      if (!json || (json && json.cacheType === '0')) {
        finished.value = true
        return
      }
      pageNo.value++
      fetchGoodsList()
    }
  } else {
    console.error('获取商品列表失败:', err.msg)
  }
}

const onLoad = () => {
  fetchGoodsList()
}

const goToDetail = (item) => {
  console.log('跳转到商品详情', item)
  router.push(`/goodsdetail/${item.goodsId}/${item.skuId}`)
}

const goToCart = () => {
  // console.log('跳转到购物车')
  router.push('/cart')
}

// 一键加入购物车
const addOneCart = debounce(async (item) => {
  try {
    showLoadingToast()

    const [err] = await addOneClick({
      bizCode: getBizCode('ORDER'),
      skuId: item.skuId,
      goodsId: item.goodsId,
      addressInfo: addressInfo.value
    })

    closeToast()

    if (err) {
      showToast(err.msg)
      return
    }

    showToast('加入购物车成功！')
    await cartStore.query()
  } catch (error) {
    closeToast()
    console.error('加入购物车失败:', error)
    showToast('加入购物车失败，请重试')
  }
}, 300)

// 组件挂载
onMounted(async () => {
  categoryId.value = route.params.id

  if (route.query.keyword) {
    searchKeyword.value = route.query.keyword
  }

  await userStore.queryDefaultAddr({ force: true })
  fetchGoodsList()
})
</script>

<style scoped lang="less">
.goods-list-page {
  padding-top: 85px;

  .page-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    .layout-toggle {
      margin-left: 12px;
      padding: 4px;
      background: none;
      border: none;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }

  .goods-content {
    padding: 0 17px;

    .skeleton-container {
      margin-top: 10px;

      .skeleton-block {
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
        border-radius: 4px;
        width: 100%;
        height: 100%;
      }

      @keyframes skeleton-loading {
        0% {
          background-position: 200% 0;
        }

        100% {
          background-position: -200% 0;
        }
      }

      // 列表布局骨架屏样式
      .list-skeleton {
        .skeleton-item {
          display: flex;
          background-color: #fff;
          border-radius: 8px;
          overflow: hidden;
          margin-bottom: 12px;
          cursor: pointer;

          .skeleton-image {
            width: 85px;
            height: 85px;
            flex-shrink: 0;
          }

          .skeleton-info {
            flex: 1;
            margin-left: 8px;
            padding-top: 8px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .skeleton-info-main {
              .skeleton-title {
                height: 13px;
                margin-bottom: 6px;
                border-radius: 4px;
                width: 80%;
              }

              .skeleton-spec {
                height: 11px;
                margin: 4px 0 8px 0;
                border-radius: 4px;
                width: 60%;
              }
            }

            .skeleton-info-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding-bottom: 8px;

              .skeleton-price {
                height: 12px;
                border-radius: 4px;
                width: 40%;
              }

              .skeleton-cart {
                width: 25px;
                height: 25px;
              }
            }
          }
        }
      }

      // 瀑布流布局骨架屏样式
      .waterfall-skeleton {
        .skeleton-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 10px;
        }

        .skeleton-item {
          background-color: #fff;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;

          .skeleton-image {
            width: 100%;
            height: 120px;
            border-radius: 8px 8px 0 0;
          }

          .skeleton-content {
            padding: 10px;

            .skeleton-title {
              height: 13px;
              margin-bottom: 6px;
              border-radius: 4px;
              width: 90%;
            }

            .skeleton-spec {
              height: 11px;
              margin: 4px 0;
              border-radius: 4px;
              width: 70%;
            }

            .skeleton-sales {
              height: 11px;
              margin: 4px 0;
              border-radius: 4px;
              width: 50%;
            }

            .skeleton-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: 8px;

              .skeleton-price {
                height: 12px;
                border-radius: 4px;
                width: 50%;
              }

              .skeleton-cart {
                width: 25px;
                height: 25px;
              }
            }
          }
        }
      }

      // 骨架屏通用样式
      .skeleton-title,
      .skeleton-spec,
      .skeleton-sales,
      .skeleton-price {
        background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
        background-size: 200% 100%;
        animation: skeleton-loading 1.5s infinite;
      }
    }

    // 列表布局样式
    .list-layout {
      margin-top: 2px;
      .goods-list-container {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: 10px;
      }

      .goods-item {
        display: flex;
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 12px;
        cursor: pointer;

        .goods-image {
          width: 85px;
          height: 85px;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .goods-info {
          flex: 1;
          margin-left: 8px;
          padding-top: 8px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .goods-info-main {
            .goods-title {
              font-size: 13px;
              color: #333;
              margin: 0;
              font-weight: normal;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              line-height: 1.5;
            }

            .goods-spec {
              font-size: 11px;
              color: #666;
              line-height: 1.5;
              margin: 4px 0 0 0;
            }
          }

          .goods-info-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 8px;

            .price-sales {
              display: flex;
              align-items: center;

              .sales-count {
                font-size: 11px;
                color: #999;
                margin-left: 12px;
              }
            }

            .cart-btn {
              background: none;
              border: none;
              padding: 0;
              cursor: pointer;

              img {
                width: 25px;
                height: 25px;
              }
            }
          }
        }
      }
    }

    // 瀑布流布局样式
    .waterfall-layout {
      .waterfall-item {
        break-inside: avoid;
        cursor: pointer;

        .waterfall-card {
          background-color: #fff;
          overflow: hidden;

          .waterfall-image {
            width: 100%;
            overflow: hidden;
            position: relative;
            border-radius: 8px;

            img {
              width: 100%;
              height: auto;
              object-fit: cover;
              border-radius: 8px;
            }
          }

          .waterfall-content {
            margin-top: 10px;

            .waterfall-title {
              font-size: 13px;
              color: #333;
              margin: 0;
              font-weight: normal;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              line-height: 1.5;
            }

            .waterfall-spec {
              font-size: 11px;
              color: #666;
              line-height: 1.5;
              margin: 4px 0 0 0;
            }

            .waterfall-sales {
              font-size: 11px;
              color: #999;
              margin: 4px 0 0 0;
            }

            .waterfall-footer {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-top: 8px;

              .cart-btn {
                background: none;
                border: none;
                padding: 0;
                cursor: pointer;

                img {
                  width: 25px;
                  height: 25px;
                }
              }
            }
          }
        }
      }
    }

    .empty-state {
      padding: 40px 0;
    }
  }
}
</style>
