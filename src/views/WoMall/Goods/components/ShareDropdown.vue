<template>
  <!-- 分享下拉菜单组件 -->
  <div
    class="share-dropdown-wrapper"
    v-show="visible"
    @click="hidePop"
  >
    <!-- 触发按钮 -->
    <button
      class="share-dropdown__trigger"
      type="button"
      @click.stop="showPop"
      :aria-expanded="show"
      aria-label="打开菜单"
    >
      <slot>
        <div class="share-dropdown__dots">
          <span class="share-dropdown__dot" />
          <span class="share-dropdown__dot" />
          <span class="share-dropdown__dot" />
        </div>
      </slot>
    </button>

    <!-- 下拉菜单 -->
    <div
      class="share-dropdown__menu"
      v-show="show"
      role="menu"
      aria-label="功能菜单"
    >
      <nav class="share-dropdown__nav" @click.stop>
        <button
          class="share-dropdown__item"
          type="button"
          role="menuitem"
          @click="toHomeHandler"
        >
          <span class="share-dropdown__icon share-dropdown__icon--home" aria-hidden="true" />
          <span class="share-dropdown__text">首页</span>
        </button>

        <button
          class="share-dropdown__item"
          type="button"
          role="menuitem"
          @click="toCategoryHandler"
        >
          <span class="share-dropdown__icon share-dropdown__icon--category" aria-hidden="true" />
          <span class="share-dropdown__text">分类</span>
        </button>

        <button
          class="share-dropdown__item"
          type="button"
          role="menuitem"
          @click="toUserHandler"
        >
          <span class="share-dropdown__icon share-dropdown__icon--user" aria-hidden="true" />
          <span class="share-dropdown__text">我的</span>
        </button>

        <button
          v-if="!isShowShare"
          class="share-dropdown__item"
          type="button"
          role="menuitem"
          @click="shareHandler"
        >
          <span class="share-dropdown__icon share-dropdown__icon--share" aria-hidden="true" />
          <span class="share-dropdown__text">分享</span>
        </button>
      </nav>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance, onActivated } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { isHarmonyOS } from 'commonkit'

defineProps({
  visible: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['share'])
const router = useRouter()
const route = useRoute()
const show = ref(false)

const isShowShare = computed(() => {
  return isHarmonyOS
})

const hidePop = () => {
  show.value = false
}

const showPop = () => {
  show.value = true
}

// 点击右上角按钮-首页
const toHomeHandler = () => {
  router.push('/home')
  show.value = false
}

// 点击右上角按钮-分类
const toCategoryHandler = () => {
  router.push({ path: '/category', query: { _t: new Date().getTime().toString() } })
  show.value = false
}

// 点击右上角按钮-我的
const toUserHandler = () => {
  router.push('/user')
  show.value = false
}

// 点击右上角按钮-分享
const shareHandler = (e) => {
  emit('share', e)
  show.value = false
}

onActivated(() => {
  show.value = false
})
</script>

<style lang="less" scoped>
.share-dropdown-wrapper {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.share-dropdown__trigger {
  .button-base();
  padding: @padding-page;
  background: transparent;
  border: none;
  cursor: pointer;
  // 优化点击体验
  -webkit-tap-highlight-color: transparent;
  transition: opacity 0.2s ease;

  &:active {
    opacity: @opacity-07;
  }
}

.share-dropdown__dots {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 3px;
  padding: @padding-page;
  background: @mask-color-065;
  border-radius: @radius-20;
  min-width: 32px;
  min-height: 32px;
  justify-content: center;
}

.share-dropdown__dot {
  width: 4px;
  height: 4px;
  background: @color-white;
  border-radius: @radius-50;
}

.share-dropdown__menu {
  position: absolute;
  right: 50%;
  bottom: 0;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: @mask-color-065;
    z-index: -1;
  }
}

.share-dropdown__nav {
  @arrow-offset: 8px;
  @arrow-size: 12px;
  @menu-shadow: 0 2px 20px rgba(0, 0, 0, 0.15);

  position: absolute;
  top: (5px + @arrow-size / 2);
  right: -(@arrow-offset + @arrow-size / 2);
  background: @bg-color-white;
  color: @text-color-primary;
  border-radius: @radius-8;
  box-shadow: @menu-shadow;
  z-index: 2;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: -(@arrow-size / 2 - 1px);
    right: @arrow-offset;
    width: @arrow-size;
    height: @arrow-size;
    background: @bg-color-white;
    transform: rotate(45deg);
    box-shadow: @menu-shadow;
  }
}

.share-dropdown__item {
  .button-base();
  display: flex;
  align-items: center;
  width: 120px;
  padding: @padding-page 12px;
  background: @bg-color-white;
  border: none;
  // border-bottom: 1px solid @divider-color-base;
  cursor: pointer;
  transition: background-color 0.2s ease;
  -webkit-tap-highlight-color: transparent;

  &:hover {
    background-color: @bg-color-gray;
  }

  &:active {
    opacity: @opacity-07;
  }

  &:last-child {
    border-bottom: none;
  }
}

// 图标
.share-dropdown__icon {
  display: inline-block;
  width: 23px;
  height: 23px;
  background-size: 23px;
  background-repeat: no-repeat;
  background-position: center center;
  flex-shrink: 0;

  &--home {
    background-image: url(../assets/icon-home.png);
  }

  &--user {
    background-image: url(../assets/icon-my.png);
  }

  &--share {
    background-image: url(../assets/icon-share.png);
  }

  &--category {
    background-image: url(../assets/icon-category.png);
  }
}

// 文本
.share-dropdown__text {
  .text-body();
  margin-left: 11px;
  flex: 1;
  text-align: left;
}

@media (max-width: 768px) {
  .share-dropdown-wrapper {
    top: 15px;
    right: 15px;
  }

  .share-dropdown__trigger {
    padding: 4px;
  }

  .share-dropdown__dots {
    min-width: 28px;
    min-height: 28px;
    padding: 6px;
    gap: 2px;
  }

  .share-dropdown__dot {
    width: 3px;
    height: 3px;
  }

  .share-dropdown__item {
    width: 100px;
    padding: 6px 10px;
  }

  .share-dropdown__icon {
    width: 20px;
    height: 20px;
    background-size: 20px;
  }

  .share-dropdown__text {
    font-size: @font-size-14;
    margin-left: 8px;
  }
}

@media (max-width: 320px) {
  .share-dropdown-wrapper {
    top: 10px;
    right: 10px;
  }

  .share-dropdown__dots {
    min-width: 24px;
    min-height: 24px;
    padding: 4px;
  }

  .share-dropdown__item {
    width: 90px;
    padding: 4px 8px;
  }

  .share-dropdown__text {
    font-size: @font-size-12;
  }
}

// 高分辨率屏幕优化
@media (-webkit-min-device-pixel-ratio: 2) {
  .share-dropdown__icon {
    image-rendering: -webkit-optimize-contrast;
  }
}
</style>
