<template>
  <MainLayout>
    <!-- 初始加载状态 - 显示骨架屏 -->
    <section v-if="isInitialLoading" class="cart">
      <CartLoadingSkeleton />
    </section>

    <!-- 未登录状态 -->
    <CartEmptyState v-else-if="!isLogin" type="login" @login="handleLogin" />

    <!-- 已登录状态 -->
    <section v-else class="cart">
      <!-- 地址选择区域 -->
      <CartHeader :address-display="addressDisplay" :is-edit-mode="isEditMode" @select-address="handleSelectAddress"
        @toggle-edit="handleEditToggle" />

      <!-- 主要内容区域 -->
      <main class="cart-main">
        <!-- 加载状态 -->
        <CartLoadingSkeleton v-if="isLoading" :show-header="false" />

        <!-- 空购物车状态 -->
        <CartEmptyState v-else-if="isEmptyState" type="empty" @go-shopping="$router.push('/home')" />

        <!-- 购物车商品列表 -->
        <section v-else class="cart-goods">
          <!-- 有效商品 -->
          <div class="cart-goods__valid" v-for="(group, groupIndex) in validGoodsList" :key="groupIndex">
            <ValidGoodsItem v-for="(item, itemIndex) in group.goodsList" :key="item.cartSkuId" :item="item"
              :ref-key="`goodsItem_${groupIndex}_${itemIndex}`" :item-index="itemIndex" :is-edit-mode="isEditMode"
              :is-edit-selected="isEditModeItemSelected(item)" @toggle-select="handleToggleItemSelect"
              @show-stepper="showStepper" @quantity-change="handleQuantityChange" @look-similar="handleLookSimilar"
              @delete-item="handleDeleteItem" @close-menu="closeLongPressMenu" @swipe-open="handleSwipeOpen"
              @swipe-close="handleSwipeClose" @set-ref="setGoodsItemRef" @long-press="handleLongPress"
              @gift-click="handleGiftClick" @content-click="handleContentClick"
              :style="{ contentVisibility: itemIndex > 5 ? 'auto' : 'visible' }" />
          </div>

          <!-- 失效商品 -->
          <WoCard v-if="hasInvalidGoods" class="cart-goods__invalid">
            <header class="cart-goods__invalid-header">
              <h3 class="cart-goods__invalid-title">{{ invalidGoodsCount }}件失效商品</h3>
              <button class="cart-goods__invalid-action" @click="handleClearInvalidGoods" type="button">
                一键清空
              </button>
            </header>
            <div class="cart-goods__invalid-list">
              <InvalidGoodsItem v-for="item in invalidGoodsList" :key="item.cartSkuId" :item="item"
                :invalid-count="invalidGoodsCount" @toggle-select="handleToggleItemSelect"
                @look-similar="handleLookSimilar" />
            </div>
          </WoCard>
        </section>
      </main>

      <!-- 底部结算栏占位符 -->
      <WoActionBarPlaceholder />
    </section>

    <!-- 底部结算栏 -->
    <WoActionBar :bottom="49" v-if="showFooter">
      <CartFooter :is-edit-mode="isEditMode" :edit-selected-count="tempSelectedItems.size"
        :is-edit-all-selected="isEditModeAllSelected" :selected-count="cartStore.selectCountAll"
        :total-price="cartStore.selectTotalPrice" :is-all-selected="cartStore.isSelectAll"
        :has-selected-goods="cartStore.hasSelectedGoods" @toggle-all-select="handleToggleAllSelect"
        @edit-toggle-all="handleEditModeToggleAll" @edit-delete="handleEditModeDelete" @checkout="handleCheckout" />
    </WoActionBar>
  </MainLayout>

  <!-- 弹窗组件 -->
  <GiftDisplayPopup v-model:visible="giftPopupVisible" :gift-list="giftList" :goods-num="currentGoodsNum" />
  <AddressQuickSelectionPopup v-model:visible="addressPopupVisible" @select="handleAddressSelect" />
</template>

<script setup>
import { ref, computed, onMounted, shallowRef, markRaw, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { showLoadingToast, closeToast, showToast } from 'vant'
import { debounce, isEmpty } from 'lodash-es'
import { useNewCartStore, CART_QUERY_STATUS } from '@/store/modules/newCart'
import { useUserStore } from '@/store/modules/user'

import WoCard from '@components/WoElementCom/WoCard.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import GiftDisplayPopup from '@/components/Common/GiftDisplayPopup.vue'
import AddressQuickSelectionPopup from '@/components/Address/AddressQuickSelectionPopup.vue'
import ValidGoodsItem from '@views/WoMall/Cart/components/ValidGoodsItem.vue'
import InvalidGoodsItem from '@views/WoMall/Cart/components/InvalidGoodsItem.vue'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'
import CartHeader from './components/CartHeader.vue'
import CartFooter from './components/CartFooter.vue'
import CartLoadingSkeleton from './components/CartLoadingSkeleton.vue'
import CartEmptyState from './components/CartEmptyState.vue'
import { getBizCode } from '@utils/curEnv.js'
import { useAlert } from '@/hooks/index.js'
import { similarity, getGiftDetails, checkOrderSku, jdAddressCheck } from '@/api/index.js'
import { BatchRequest } from '@/utils/tools.js'
import { buyProductCart, buyProductCartSession } from '@/utils/storage.js'

const $alert = useAlert()
const router = useRouter()
const cartStore = useNewCartStore()
const userStore = useUserStore()

// 弹窗状态
const giftPopupVisible = ref(false) // 赠品弹窗显示状态
const addressPopupVisible = ref(false) // 地址选择弹窗显示状态

// 赠品相关状态
const giftList = shallowRef([]) // 当前商品的赠品列表
const currentGoodsNum = ref(0) // 当前商品数量
const cacheSkuGiftDetailsList = markRaw(new Map()) // 赠品详情缓存

// 编辑模式相关状态
const isEditMode = ref(false) // 是否处于编辑模式
const tempSelectedItems = shallowRef(new Set()) // 编辑模式下临时选中的商品ID集合

// 商品交互状态
const longPressedItem = shallowRef(null) // 当前长按的商品项
const goodsItemRefs = shallowRef({}) // 商品组件引用集合

// 页面初始化状态
const isInitialLoading = ref(true) // 初始加载状态，用于控制骨架屏显示
const isFirstRender = ref(true) // 首次渲染标记，用于优化首屏性能



// 用户状态相关
const isLogin = computed(() => userStore.isLogin)

// 地址显示逻辑
const addressDisplay = computed(() => {
  const addr = userStore.curAddressInfo
  if (!addr) return '请选择收货地址'
  return addr.addrDetail || `${addr.provinceName}${addr.cityName}${addr.countyName}`
})

// 页面状态相关
const isLoading = computed(() => cartStore.getCartLoadingStatus === CART_QUERY_STATUS.LOADING)
const isEmptyState = computed(() =>
  !cartStore.hasValidGoods &&
  !cartStore.hasInvalidGoods &&
  cartStore.getCartLoadingStatus === CART_QUERY_STATUS.SUCCESS
)
const showFooter = computed(() =>
  cartStore.getCartLoadingStatus === CART_QUERY_STATUS.SUCCESS && cartStore.hasValidGoods
)

// 商品列表相关
const validGoodsList = computed(() => cartStore.getCartValidList)
const invalidGoodsList = computed(() => cartStore.getCartInvalidList[0]?.goodsList || [])
const hasInvalidGoods = computed(() => cartStore.hasInvalidGoods)
const invalidGoodsCount = computed(() => invalidGoodsList.value.length)

// 编辑模式相关
const totalValidItems = computed(() =>
  validGoodsList.value.reduce((total, group) => total + group.goodsList.length, 0)
)
const isEditModeAllSelected = computed(() =>
  totalValidItems.value > 0 && tempSelectedItems.value.size === totalValidItems.value
)

// 检查收货地址是否有效
const addressCheck = async () => {
  const [err, json] = await jdAddressCheck()
  if (err) {
    showToast(err.msg)
    return false
  }

  // 地址无效时提示用户修改
  if (!json) {
    await $alert({
      message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存',
      confirmButtonText: '修改地址',
      showCancelButton: true,
      cancelButtonText: '取消',
      onConfirmCallback: () => {
        router.push({
          name: 'address-edit',
          query: {
            addrId: userStore.curAddressInfo.addressId,
            isInvalid: '1'
          }
        })
      },
      onCancelCallback: () => { }
    })
    return false
  }
  return true
}

// 初始化商品的UI交互属性
const initializeGoodsProperties = () => {
  validGoodsList.value.forEach(group => {
    group.goodsList.forEach(item => {
      Object.assign(item, {
        stepperVisible: item.stepperVisible ?? false, // 步进器显示状态
        isSwipeOpen: item.isSwipeOpen ?? false, // 滑动菜单打开状态
        showLongPressMenu: item.showLongPressMenu ?? false // 长按菜单显示状态
      })
    })
  })
}

// 初始化购物车
const initializeCart = async () => {
  try {
    // 检查用户登录状态
    await userStore.queryLoginStatus()

    // 无论登录状态如何，都结束初始加载状态
    isInitialLoading.value = false

    if (userStore.isLogin) {
      showLoadingToast()

      // 验证收货地址
      const addressValid = await addressCheck()
      if (addressValid) {
        // 获取购物车数据并初始化商品属性
        await cartStore.query()
        initializeGoodsProperties()
      }
      closeToast()
    }
  } catch (error) {
    closeToast()
    // 确保即使出错也结束初始加载状态
    isInitialLoading.value = false
    console.error('初始化购物车失败:', error)
  }
}

// 地址选择后的防抖处理
const debouncedAddressSelect = debounce(async () => {
  try {
    // 地址选择后不需要重新显示初始加载状态，直接更新购物车数据
    if (userStore.isLogin) {
      showLoadingToast()

      // 验证收货地址
      const addressValid = await addressCheck()
      if (addressValid) {
        // 获取购物车数据并初始化商品属性
        await cartStore.query()
        initializeGoodsProperties()
      }
      closeToast()
    }
  } catch (error) {
    closeToast()
    console.error('更新地址失败:', error)
    showToast('更新地址失败')
  }
}, 300)

// 全选操作的防抖处理
const debouncedToggleAllSelect = debounce(async () => {
  try {
    showLoadingToast()
    await cartStore.checkedAllValid()
    closeToast()
  } catch (error) {
    console.error('全选操作失败:', error)
  }
}, 300)

// 商品数量变更的防抖处理
const debouncedQuantityChange = debounce(async (item) => {
  try {
    showLoadingToast()
    const error = await cartStore.updateGoodsNum({
      goodsId: item.cartGoodsId,
      skuId: item.cartSkuId,
      goodsNum: item.skuNum
    })
    closeToast()
    // 处理库存不足的情况
    if (error) {
      if (error.code === 'FE2001' && error.stock !== undefined) {
        // 使用 nextTick 确保DOM更新完成后再修改数据
        await nextTick()
        // 找到对应商品并更新为实际库存数量
        const targetItem = validGoodsList.value
          .flatMap(group => group.goodsList)
          .find(goods => goods.cartGoodsId === item.cartGoodsId && goods.cartSkuId === item.cartSkuId)

        if (targetItem) {
          targetItem.skuNum = error.stock
        }
      }
      showToast(error.msg || '更新商品数量失败')
    }
  } catch (error) {
    closeToast()
    console.error('更新商品数量异常:', error)
    showToast('更新商品数量失败')
  }
}, 300) // 减少防抖时间，提升响应速度

// 用户登录处理
const handleLogin = () => userStore.login()

// 显示地址选择弹窗
const handleSelectAddress = () => {
  addressPopupVisible.value = true
}

// 地址选择完成后的处理
const handleAddressSelect = () => debouncedAddressSelect()

// 切换编辑模式
const handleEditToggle = () => {
  if (isEditMode.value) {
    tempSelectedItems.value.clear()
  }
  isEditMode.value = !isEditMode.value
}

// 处理商品选中状态切换
const handleToggleItemSelect = async (item) => {
  if (isEditMode.value) {
    // 编辑模式下只更新临时选中状态
    handleEditModeSelect(item)
  } else {
    // 普通模式下更新购物车选中状态
    try {
      const newSelectState = item.selected !== 'true'
      await cartStore.updateGoodsSelectMuti([{
        goodsId: item.cartGoodsId,
        skuId: item.cartSkuId,
        select: newSelectState
      }])
    } catch (error) {
      console.error('更新商品选中状态失败:', error)
    }
  }
}

// 处理全选切换
const handleToggleAllSelect = () => debouncedToggleAllSelect()

// 显示商品数量步进器
const showStepper = (item) => {
  item.stepperVisible = true
}

// 处理商品数量变更
const handleQuantityChange = (item) => debouncedQuantityChange(item)

// 删除单个商品
const handleDeleteItem = async (item) => {
  try {
    await cartStore.removeMuti(item)
  } catch (error) {
    // console.error('删除商品失败:', error)
  }
}

// 一键清空失效商品
const handleClearInvalidGoods = async () => {
  try {
    const deleteGoodsList = invalidGoodsList.value.map(item => ({
      goodsId: item.cartGoodsId,
      skuId: item.cartSkuId
    }))
    await cartStore.removeInvalidGoods({ deleteGoodsList })
  } catch (error) {
    // console.error('清空失效商品失败:', error)
  }
}

// 处理商品滑动菜单打开
const handleSwipeOpen = (item) => {
  item.isSwipeOpen = true
}

// 处理商品滑动菜单关闭
const handleSwipeClose = (item) => {
  item.isSwipeOpen = false
}

// 处理商品长按事件
const handleLongPress = (item) => {
  // 关闭其他商品的长按菜单
  validGoodsList.value.forEach(group => {
    group.goodsList.forEach(good => {
      if (good !== item) {
        good.showLongPressMenu = false
      }
    })
  })

  // 显示当前商品的长按菜单
  longPressedItem.value = item
  item.showLongPressMenu = true
}

// 关闭长按菜单
const closeLongPressMenu = () => {
  if (longPressedItem.value) {
    longPressedItem.value.showLongPressMenu = false
    longPressedItem.value = null
  }
}

// 设置商品组件引用
const setGoodsItemRef = (el, key) => {
  goodsItemRefs.value[key] = el
}

// 处理编辑模式下的商品选择
const handleEditModeSelect = (item) => {
  const itemId = `${item.cartGoodsId}_${item.cartSkuId}`
  if (tempSelectedItems.value.has(itemId)) {
    tempSelectedItems.value.delete(itemId)
  } else {
    tempSelectedItems.value.add(itemId)
  }
}

// 编辑模式下的全选/取消全选
const handleEditModeToggleAll = () => {
  if (tempSelectedItems.value.size === 0) {
    // 全选所有有效商品
    validGoodsList.value.forEach(group => {
      group.goodsList.forEach(item => {
        const itemId = `${item.cartGoodsId}_${item.cartSkuId}`
        tempSelectedItems.value.add(itemId)
      })
    })
  } else {
    // 取消全选
    tempSelectedItems.value.clear()
  }
}

// 编辑模式下的批量删除
const handleEditModeDelete = async () => {
  if (tempSelectedItems.value.size === 0) return

  try {
    await $alert({
      title: '确认删除',
      message: `确定要删除选中的 ${tempSelectedItems.value.size} 件商品吗？`,
      showCancelButton: true,
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      onConfirmCallback: async () => {
        // 构建删除商品列表
        const deleteGoodsList = Array.from(tempSelectedItems.value).map(itemId => {
          const [goodsId, skuId] = itemId.split('_')
          return { goodsId, skuId }
        })

        // 执行删除并退出编辑模式
        await cartStore.removeMuti(deleteGoodsList)
        isEditMode.value = false
        tempSelectedItems.value.clear()
      }
    })
  } catch (error) {
    // console.error('删除商品失败:', error)
  }
}

// 判断商品在编辑模式下是否被选中
const isEditModeItemSelected = (item) => {
  const itemId = `${item.cartGoodsId}_${item.cartSkuId}`
  return tempSelectedItems.value.has(itemId)
}

// 获取赠品详情信息
const getGiftsDetails = async (goodsInfo, giftList) => {
  const skuId = goodsInfo.goods?.skuList?.[0]?.skuId
  const supplierCode = goodsInfo.goods?.supplierCode || 'jd_yg'

  // 参数校验
  if (!skuId || isEmpty(giftList)) return []

  // 检查缓存
  if (cacheSkuGiftDetailsList.has(skuId)) {
    return cacheSkuGiftDetailsList.get(skuId)
  }

  // 批量请求赠品详情
  const batchRequest = new BatchRequest()
  const tempGiftDetailsList = []

  giftList.forEach((item, index) => {
    const promise = getGiftDetails({
      supplierSkuId: item.giftId,
      supplierCode
    }).then(([err, json]) => {
      // 构建赠品详情对象
      const giftDetail = err ? {} : {
        ...json,
        giftType: item.giftType,
        giftNum: item.giftNum,
        belongToSkuMaxNum: item.belongToSkuMaxNum,
        belongToSkuMinNum: item.belongToSkuMinNum
      }
      tempGiftDetailsList.splice(index, 0, giftDetail)
    })

    batchRequest.push(promise)
  })

  // 返回Promise，等待所有请求完成
  return new Promise((resolve) => {
    batchRequest.onComplete = () => {
      // 缓存结果
      cacheSkuGiftDetailsList.set(skuId, tempGiftDetailsList)
      resolve(tempGiftDetailsList)
    }
  })
}

// 处理赠品点击事件
const handleGiftClick = async (data) => {
  const { goods: goodsInfo, giftList: goodsInfoGiftList } = data

  try {
    showLoadingToast()
    currentGoodsNum.value = goodsInfo.skuNum || 0

    // 获取赠品详情
    const giftDetails = await getGiftsDetails(goodsInfo, goodsInfoGiftList)
    giftList.value = giftDetails.filter(item => !isEmpty(item))

    closeToast()
    giftPopupVisible.value = true
  } catch (error) {
    closeToast()
    // console.error('获取赠品详情失败:', error)
    // 即使获取失败也显示弹窗，避免用户无响应
    giftList.value = []
    currentGoodsNum.value = goodsInfo.skuNum || 0
    giftPopupVisible.value = true
  }
}

// 查看相似商品
const handleLookSimilar = async (item) => {
  try {
    showLoadingToast()
    // 兼容不同的商品ID字段
    const goodsId = typeof item === 'string' ? item : item.cartGoodsId || item.goodsId

    const [err, json] = await similarity({
      goodsId,
      bizCode: getBizCode('GOODS')
    })

    closeToast()

    if (!err && json) {
      // 跳转到相似商品列表页
      router.push(`/goodslist/${json}`)
    } else {
      // 失败时跳转到分类页
      router.push({ path: '/category' })
    }
  } catch (error) {
    closeToast()
    // console.error('查看相似商品失败:', error)
    router.push({ path: '/category' })
  }
}

// 处理商品内容点击事件 - 跳转到商品详情页
const handleContentClick = (data) => {
  const { goodsId, skuId } = data
  router.push(`/goodsdetail/${goodsId}/${skuId}`)
}

// 处理结算流程
const handleCheckout = async () => {
  // 检查是否有选中的商品
  if (cartStore.selectCountAll < 1) {
    showToast('请选择下单商品')
    return
  }

  try {
    // 构建购买商品列表
    const buyGoodsList = validGoodsList.value
      .flatMap(group => group.goodsList)
      .filter(item => item.selected === 'true')
      .map(item => ({
        cartGoodsId: item.cartGoodsId,
        cartSkuId: item.cartSkuId,
        skuNum: item.skuNum,
        supplierCode: item.supplierCode,
        nowPrice: item.nowPrice
      }))

    // 二次检查商品列表
    if (isEmpty(buyGoodsList)) {
      showToast('请选择下单商品')
      return
    }

    // 保存购买商品到本地存储
    buyProductCart.set(buyGoodsList)
    buyProductCartSession.set(buyGoodsList)

    // 检查收货地址
    const info = userStore.curAddressInfo
    if (!info) {
      showToast('请先选择收货地址')
      return
    }

    // 构建地址信息
    const addressInfo = JSON.stringify({
      provinceId: info.provinceId,
      provinceName: info.provinceName,
      cityId: info.cityId,
      cityName: info.cityName,
      countyId: info.countyId,
      countyName: info.countyName,
      townId: info.townId,
      townName: info.townName
    })

    // 订单SKU验证
    showLoadingToast()
    const [err] = await checkOrderSku({
      bizCode: getBizCode('ORDER'),
      addressInfo,
      buyGoodsList: JSON.stringify(buyGoodsList)
    })
    closeToast()

    if (!err) {
      // 验证成功，跳转到订单确认页
      router.push('/orderconfirm')
    } else {
      // 验证失败，显示错误信息并刷新购物车
      showToast(err.msg || '订单验证失败')

      // 延迟刷新购物车，避免频繁操作
      setTimeout(async () => {
        try {
          showLoadingToast()
          await cartStore.query()
          closeToast()
        } catch (refreshError) {
          closeToast()
          // console.error('刷新购物车失败:', refreshError)
        }
      }, 1500)
    }
  } catch (error) {
    closeToast()
    // console.error('结算处理失败:', error)
    showToast('结算失败，请重试')
  }
}

onMounted(() => {
  // 使用 nextTick 确保DOM渲染完成后再初始化
  nextTick(() => {
    // 使用 requestIdleCallback 优化初始化时机，避免阻塞关键渲染
    if (window.requestIdleCallback) {
      window.requestIdleCallback(() => {
        initializeCart()
      }, { timeout: 100 })
    } else {
      // 降级方案：使用 setTimeout
      setTimeout(() => {
        initializeCart()
      }, 0)
    }
  })

  // 标记首次渲染完成
  isFirstRender.value = false
})
</script>
<style scoped lang="less">
// 购物车主容器
.cart {
  height: 100%;
  padding: 10px 10px 0 10px;
  box-sizing: border-box;
  background: #F8F9FA;
  user-select: none;
  overflow: auto;
  // 性能优化：使用contain属性限制重排重绘范围
  contain: layout style paint;
  // 启用硬件加速
  transform: translateZ(0);
  will-change: scroll-position;
  backface-visibility: hidden;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  // 优化渲染性能
  content-visibility: auto;
}

// 购物车主要内容区域
.cart-main {
  flex: 1;
  overflow: hidden;
}

// 购物车商品列表样式
.cart-goods {
  // 优化列表渲染性能
  contain: layout style;

  &__valid {
    margin-bottom: 10px;
    // 优化长列表性能
    contain: layout style;
  }

  &__invalid {
    margin-top: 10px;
    contain: layout style;
  }

  &__invalid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  &__invalid-title {
    font-size: @font-size-14;
    font-weight: @font-weight-600;
    color: @text-color-primary;
    margin: 0;
  }

  &__invalid-action {
    font-size: @font-size-14;
    color: @theme-color;
    font-weight: @font-weight-600;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    touch-action: manipulation;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.7;
    }
  }
}
</style>
