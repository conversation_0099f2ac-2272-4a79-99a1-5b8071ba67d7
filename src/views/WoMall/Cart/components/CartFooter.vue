<template>
  <footer class="cart-footer">
    <!-- 编辑模式 -->
    <template v-if="isEditMode">
      <button class="cart-footer__select-all" @click="$emit('edit-toggle-all')" type="button">
        <img
          :src="isEditAllSelected ? woSelectImg : noSelectImg"
          alt="全选"
          class="cart-footer__checkbox-icon"
          loading="eager"
          decoding="async"
        />
        <span class="cart-footer__select-text">全选</span>
      </button>
      <div class="cart-footer__edit-info">
        已选{{ editSelectedCount }}件
      </div>
      <WoButton
        type="gradient"
        size="medium"
        class="cart-footer__action-btn"
        :disabled="editSelectedCount === 0"
        @click="$emit('edit-delete')"
      >
        删除
      </WoButton>
    </template>

    <!-- 正常模式 -->
    <template v-else>
      <button class="cart-footer__select-all" @click="$emit('toggle-all-select')" type="button">
        <img
          :src="isAllSelected ? woSelectImg : noSelectImg"
          alt="全选"
          class="cart-footer__checkbox-icon"
          loading="eager"
          decoding="async"
        />
        <span class="cart-footer__select-text">全选</span>
      </button>
      <div class="cart-footer__price-info">
        已选{{ selectedCount }}件，合计
        <PriceDisplay :price="totalPrice" size="medium" color="orange" />
      </div>
      <WoButton
        type="gradient"
        size="medium"
        class="cart-footer__action-btn"
        :disabled="!hasSelectedGoods"
        @click="$emit('checkout')"
      >
        结算
      </WoButton>
    </template>
  </footer>
</template>

<script setup>
import { toRefs } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import PriceDisplay from '@/components/Common/PriceDisplay.vue'
import noSelectImg from '@/static/images/no-select.png'
import woSelectImg from '@/static/images/wo-select.png'

const props = defineProps({
  isEditMode: {
    type: Boolean,
    default: false
  },
  editSelectedCount: {
    type: Number,
    default: 0
  },
  isEditAllSelected: {
    type: Boolean,
    default: false
  },
  selectedCount: {
    type: Number,
    default: 0
  },
  totalPrice: {
    type: [String, Number],
    default: '0'
  },
  isAllSelected: {
    type: Boolean,
    default: false
  },
  hasSelectedGoods: {
    type: Boolean,
    default: false
  }
})

const {
  isEditMode,
  editSelectedCount,
  isEditAllSelected,
  selectedCount,
  totalPrice,
  isAllSelected,
  hasSelectedGoods
} = toRefs(props)

defineEmits(['toggle-all-select', 'edit-toggle-all', 'edit-delete', 'checkout'])
</script>

<style scoped lang="less">
.cart-footer {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 10px;

  &__select-all {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    touch-action: manipulation;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.7;
    }
  }

  &__checkbox-icon {
    width: 18px;
    height: 18px;
    margin-right: 5px;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  &__select-text {
    font-size: @font-size-13;
    color: @text-color-primary;
  }

  &__price-info {
    flex: 1;
    font-size: @font-size-14;
    color: @text-color-primary;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;

    .price {
      font-size: @font-size-16;
      font-weight: @font-weight-600;
      color: @theme-color;
    }
  }

  &__edit-info {
    flex: 1;
    font-size: @font-size-14;
    color: @text-color-primary;
    display: flex;
    align-items: center;
  }

  &__action-btn {
    flex-shrink: 0;
  }
}

@media (max-width: 768px) {
  .cart-footer {
    &__select-text {
      font-size: @font-size-12;
    }

    &__price-info {
      font-size: @font-size-13;
    }

    &__edit-info {
      font-size: @font-size-13;
    }
  }
}
</style>
