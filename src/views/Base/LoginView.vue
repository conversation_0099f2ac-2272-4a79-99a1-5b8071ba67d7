<template>
  <!-- 登录页面主模板：使用通用登录组件LoginComponent -->
  <!-- 传入isLocal(环境标识)和options(登录配置项)作为组件属性 -->
  <LoginComponent :isLocal="isLocal" :options="options" />
</template>

<script setup>
import { ref, computed } from 'vue'
import { LoginComponent } from 'commonkit-login-vue3'
import { loginType } from '@utils/storage.js'

const isLocal = ref(import.meta.env.VITE_ENV === 'local')

// 登录组件的配置选项、动态生成并返回组件所需的配置参数
const options = computed(() => ({
  useHistoryReplace: true, // 启用history.replaceState导航模式，避免登录后回退到登录页
  loginType: loginType.get() || '0'
}))
</script>
