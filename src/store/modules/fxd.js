import { defineStore } from 'pinia'
import { queryIncomeInfo, queryOrderList, checkSignature } from '@api/interface/fxd.js'
import { curDeveloperId } from '@utils/storage.js'

const storage = window.sessionStorage

export const useFxdStore = defineStore('fxd', {
  state: () => ({
    salesmanId: '', // 能人 id，用于查看能人收益和订单
    developerId: '', // 发展人 id，能人也属于发展人，用于用户购买商品时数据统计
    distribizCode: '',
    distribizCodeList: ['ziying', 'st-dbgj'],
    info: {
      // 预估收益
      expect: '0.00',
      real: '0.00',
      count: '0'
    },
    orderList: [],
    isValid: false
  }),
  actions: {
    setSalesmanId(payload) {
      this.salesmanId = payload.salesmanId
      this.distribizCode = payload.distribizCode
      storage.setItem('salesmanId', payload.salesmanId)
      storage.setItem('distribizCode', payload.distribizCode)
    },
    setIncomeInfo(payload) {
      payload.estimatedIncome && (this.info.expect = (payload.estimatedIncome / 100).toFixed(2))
      payload.payedIncome && (this.info.real = (payload.payedIncome / 100).toFixed(2))
      payload.estimatedIncomeCount && (this.info.count = payload.estimatedIncomeCount)
    },
    setOrderList(payload) {
      // 数据转换，将后台接口数据转换成前端页面数据
      const list = payload.map(item => {
        let count = 0
        let supplierName = ''
        let totalAmt = 0
        const supplier = item.supplierOrder[0] // 目前一个主订单下只有一个供应商订单，因此取第一个数据即可

        if (!supplier) {
          return {}
        }
        const _item = {
          parentId: item.orderId,
          grandResultBz: item.grandResultBz, // 佣金是否发放：0未申请  1申请成功 2已发放
          id: supplier.supplierOrderId,
          key: `${item.orderId}_${supplier.supplierOrderId}`,
          name: supplierName,
          status: supplier.supplierOrderState, // 订单状态：1 待收货；2 已签收；3 已取消
          totalCount: '',
          totalAmt: supplier.price / 100,
          totalIncome: supplier.commission / 100,
          list: (supplier.skuNumInfo || []).map(item => {
            count += Number(item.skuNum)
            if (item.sku.supplierName) supplierName = item.sku.supplierName
            const promotion = item.sku.skuPromotionList && item.sku.skuPromotionList[0]

            if (promotion) {
              totalAmt += (promotion.promotionPrice / 100) * item.skuNum
            } else {
              totalAmt += (item.sku.price / 100) * item.skuNum
            }

            let price = item.sku.price / 100
            if (item.loanOrder && item.loanOrder.loanProduct && item.loanOrder.loanProduct.loanAmount) {
              // 花呗支付，展示借款金额
              price = item.loanOrder.loanProduct.loanAmount / 100
            } else if (promotion && promotion.promotionPrice) {
              // 促销产品，展示促销价格
              price = promotion.promotionPrice / 100
            }

            return {
              id: item.sku.skuId,
              img: item.sku.detailImageUrl,
              name: item.sku.name,
              price,
              count: item.skuNum,
              // 后台不返回是否是推广商品的状态，目前一个订单只有一个商品，
              // 因此根据订单的佣金 commission 来判断
              type: Number(supplier.commission) === 0 ? '0' : '1'
            }
          })
        }

        if (totalAmt) _item.totalAmt = totalAmt
        if (item.loanOrder && item.loanOrder.loanProduct && item.loanOrder.loanProduct.loanAmount) {
          // 花呗订单需要展示借款金额
          _item.totalAmt = item.loanOrder.loanProduct.loanAmount / 100
        }
        if (_item.status === '2' || _item.status === '10') {
          // 已取消（未支付）、已退款订单，需要将佣金收益重置为 0
          _item.totalIncome = 0
        }
        _item.name = supplierName
        _item.totalCount = count

        return _item
      }).filter(item => !!item.id)

      this.orderList = Object.freeze(list)
    },
    setDeveloperId(payload) {
      this.developerId = payload.developerId || ''
      curDeveloperId.set(this.developerId)
    },
    // 有 fxdtoken 重新查，没有则读取缓存
    async querySalesmanId(payload) {
      let salesmanId = ''
      let distribizCode = ''

      if (payload && payload.fxdtoken) {
        distribizCode = payload.distribizCode || ''
        const [, res] = await checkSignature({
          fxdtoken: payload.fxdtoken,
          distribizCode: distribizCode || 'st-dbgj'
        })

        if (res && res.code === '0000') {
          salesmanId = res.data
        }
        // mock
        // salesmanId = 'fxd248dc6433f65ffa25c2e79f6bf4631f3'
      } else {
        salesmanId = storage.getItem('salesmanId') || ''
        distribizCode = storage.getItem('distribizCode') || ''
      }

      this.setSalesmanId({ salesmanId, distribizCode })
    },
    async queryIncomeInfo(payload) {
      await this.querySalesmanId({ fxdtoken: payload.fxdtoken, distribizCode: payload.distribizCode })

      if (!this.salesmanId) return

      if (this.distribizCode) {
        // 指定了 distribizCode
        const [err, json] = await queryIncomeInfo({ shopCode: this.salesmanId, distribizCode: this.distribizCode })

        if (!err) {
          this.setIncomeInfo(json || {})
        }
      } else {
        // 未指定 distribizCode，后台不支持查询全部，只能遍历查询
        const dataLists = await Promise.all(this.distribizCodeList.map(code => {
          return queryIncomeInfo({
            shopCode: this.salesmanId,
            distribizCode: code
          }).then(res => {
            const [err, json] = res
            return !err && json ? json : {}
          })
        }))

        const data = dataLists.reduce((acc, curr) => {
          return {
            estimatedIncome: Number(acc.estimatedIncome) + Number(curr.estimatedIncome),
            payedIncome: Number(acc.payedIncome) + Number(curr.payedIncome),
            estimatedIncomeCount: Number(acc.estimatedIncomeCount) + Number(curr.estimatedIncomeCount)
          }
        })
        this.setIncomeInfo(data)
      }
    },
    async queryOrderList(payload) {
      await this.querySalesmanId()

      if (!this.salesmanId) return

      if (this.distribizCode) {
        // 指定了 distribizCode
        const [err, json] = await queryOrderList(
          { shopCode: this.salesmanId, orderState: payload.orderType, distribizCode: this.distribizCode })

        if (!err && json) {
          this.setOrderList(json || [])
        }
      } else {
        // 未指定 distribizCode，后台不支持查询全部，只能遍历查询
        const dataLists = await Promise.all(this.distribizCodeList.map(code => {
          return queryOrderList({
            shopCode: this.salesmanId,
            orderState: payload.orderType,
            distribizCode: code
          }).then(res => {
            const [err, json] = res
            return !err && json ? json : []
          })
        }))

        const list = dataLists.reduce((acc, curr) => {
          return acc.concat(curr)
        }, []).sort((prev, next) => next.orderId - prev.orderId)
        this.setOrderList(list)
      }
    },
    async getDeveloperId() {
      const cache = curDeveloperId.get()
      if (cache) {
        this.setDeveloperId({ developerId: cache })
      }
    }
  }
})
