// 售后服务
import { defineStore } from 'pinia'

export const useAfterSalesStore = defineStore('afterSales', {
  state: () => ({
    applyType: '',
    afterSaleState: '',
    bizOrderId: '',
    bizCode: '',
    orderState: ''
  }),

  actions: {
    updateAfterSalesInfo(payload) {
      if (payload.applyType !== undefined) this.applyType = payload.applyType
      if (payload.afterSaleState !== undefined) this.afterSaleState = payload.afterSaleState
      if (payload.bizOrderId !== undefined) this.bizOrderId = payload.bizOrderId
      if (payload.bizCode !== undefined) this.bizCode = payload.bizCode
      if (payload.orderState !== undefined) this.orderState = payload.orderState
    }
  },

  getters: {
    getAfterSalesInfo: (state) => state
  }
})
