import { defineStore } from 'pinia'
import { getOrderCount } from '@/api/interface/order'
import { debounce } from 'lodash-es'

export const useOrderStore = defineStore('order', {
  state: () => ({
    unpaidCount: 0,      // 未支付订单数
    processedCount: 0,   // 待发货订单数
    dispatchedCount: 0,  // 待收货订单数
    loading: false,      // 加载状态
    lastFetchTime: 0     // 上次获取时间，用于缓存
  }),

  getters: {
    // 获取指定类型的订单数量
    getCountByType: (state) => (type) => {
      switch (type) {
        case '0': return state.unpaidCount
        case '3': return state.processedCount
        case '5': return state.dispatchedCount
        default: return 0
      }
    },
    
    // 是否有任何未处理订单
    hasUnprocessedOrders: (state) => {
      return state.unpaidCount > 0 || state.processedCount > 0 || state.dispatchedCount > 0
    }
  },

  actions: {
    // 设置订单数量
    setOrderCounts({ unpayCount = 0, payedCount = 0, deliverCount = 0 }) {
      this.unpaidCount = unpayCount
      this.processedCount = payedCount
      this.dispatchedCount = deliverCount
    },

    // 防抖获取订单数量
    fetchOrderCountDebounced: debounce(async function() {
      await this.fetchOrderCount()
    }, 300),

    // 获取订单数量
    async fetchOrderCount(force = false) {
      // 缓存5分钟
      const now = Date.now()
      if (!force && this.lastFetchTime && (now - this.lastFetchTime) < 5 * 60 * 1000) {
        return
      }

      if (this.loading) return
      
      this.loading = true
      try {
        const [err, data] = await getOrderCount()
        if (!err && data) {
          this.setOrderCounts(data)
          this.lastFetchTime = now
        }
      } catch (error) {
        console.error('获取订单数量失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 清除缓存
    clearCache() {
      this.lastFetchTime = 0
    },

    // 重置状态
    reset() {
      this.unpaidCount = 0
      this.processedCount = 0
      this.dispatchedCount = 0
      this.loading = false
      this.lastFetchTime = 0
    }
  }
})