import { defineStore } from 'pinia'
import { urlAppend, useLogin, log } from 'commonkit'
import { queryUserAddrList, queryUserDefaultAddr } from '@api/interface/address.js'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz, curTempAddrInfo, loginType } from '@utils/storage.js'

export const useUserStore = defineStore('user', {
  state: () => ({
    isLogin: null, // null-未查询，true-已登录，false-未登录
    cifUserId: null,
    addressInfoTemp: curTempAddrInfo.get() || [], // 用户临时选择的地址（数组形式，Vant-Cascader 数据结构）
    addressInfo: null, // 用户的地址信息
    addressList: null, // 用户地址列表
    addressInfoDefault: {
      provinceId: '1',
      provinceName: '北京',
      cityId: '72',
      cityName: '朝阳区',
      countyId: '4137',
      countyName: '管庄地区',
      townId: '0',
      townName: ''
    } // 用户没有地址情况下，默认的地址
  }),
  getters: {
    curAddressInfo() {
      const addrInfo = this.addressInfo
      const addrInfoDefault = this.addressInfoDefault
      const addrInfoTemp = this.addressInfoTemp
      if (addrInfoTemp[0]) {
        // 存在临时地址，直接返回临时地址数据
        const province = addrInfoTemp[0]
        const city = addrInfoTemp[1]
        const county = addrInfoTemp[2]
        const town = addrInfoTemp[3]
        return {
          type: 3,
          addressId: '',
          isDefault: false,
          provinceId: province ? province.areaId : '0',
          provinceName: province ? province.areaName : '',
          cityId: city ? city.areaId : '0',
          cityName: city ? city.areaName : '',
          countyId: county ? county.areaId : '0',
          countyName: county ? county.areaName : '',
          townId: town ? town.areaId : '0',
          townName: town ? town.areaName : '',
          addrDetail: '',
          recName: '',
          recPhone: ''
        }
      } else if (addrInfo?.provinceId) {
        // 存在用户真实地址
        return {
          type: 1,
          addressId: addrInfo.addressId,
          isDefault: addrInfo.isDefault === '1',
          provinceId: addrInfo.provinceId || '0',
          provinceName: addrInfo.provinceName,
          cityId: addrInfo.cityId || '0',
          cityName: addrInfo.cityName,
          countyId: addrInfo.countyId || '0',
          countyName: addrInfo.countyName,
          townId: addrInfo.townId || '0',
          townName: addrInfo.townName,
          addrDetail: addrInfo.addrDetail,
          recName: addrInfo.recName,
          recPhone: addrInfo.recPhone
        }
      } else {
        // 返回默认北京-管庄地址
        return {
          type: 2,
          addressId: '',
          isDefault: false,
          provinceId: addrInfoDefault.provinceId,
          provinceName: addrInfoDefault.provinceName,
          cityId: addrInfoDefault.cityId,
          cityName: addrInfoDefault.cityName,
          countyId: addrInfoDefault.countyId,
          countyName: addrInfoDefault.countyName,
          townId: addrInfoDefault.townId,
          townName: addrInfoDefault.townName,
          addrDetail: '',
          recName: '',
          recPhone: ''
        }
      }
    },
    getAddressInfo() {
      return this.addressInfo
    }
  },
  actions: {
    setLoginStatus(payload) {
      this.isLogin = payload
    },
    setDefaultAddr(payload) {
      if (!payload) return
      this.addressInfo = payload
    },
    setTempAddr(payload) {
      this.addressInfoTemp = payload || []
      curTempAddrInfo.set(payload)
    },
    setAddrList(payload) {
      if (!payload) return
      this.addressList = payload
    },
    setCifUserId(payload) {
      this.cifUserId = payload
    },
    async queryDefaultAddr({ force = false } = {}) {
      log('user.js queryDefaultAddr 查询登录')
      await this.queryLoginStatus()
      if (!this.isLogin) return // 未登录
      if (!force && this.addressInfo?.provinceId) return // 已经查询过默认地址
      const [err, json] = await queryUserDefaultAddr()
      if (!err) {
        this.setDefaultAddr(json || {})
      }
    },
    async queryAddrList({ force = false } = {}) {
      log('user.js queryAddrList 查询登录')
      await this.queryLoginStatus()
      if (!this.isLogin) return // 未登录
      if (!force && this.addressList) return // 已经查询过默认地址
      const [err, json] = await queryUserAddrList()
      if (!err) {
        this.setAddrList(json || [])
      }
    },
    async queryLoginStatus() {
      if (typeof this.isLogin === 'boolean') {
        // 已经有了明确结果，不在查询了
        log('user.js queryLoginStatus 当前已存在登录状态', this.isLogin)
        return
      }
      const [loginState, queryStatus] = useLogin()
      // 查询登录状态
      await queryStatus({ useHistoryReplace: true, loginType: loginType.get() || '0' })
      // loginState.status 用户登录状态，0-未检查，-1-登录错误，1-未登录，2-已登录
      const isLogin = loginState.status === 2
      const cifId = loginState.cifId
      log('user.js queryLoginStatus 查询登录状态', isLogin)
      this.setLoginStatus(isLogin)
      this.setCifUserId(cifId)
    },
    async login(payload = {}) {
      const reload = typeof payload.reload === 'boolean' ? payload.reload : true
      if (this.isLogin) {
        // 已登录，直接刷新防止页面没处理状态
        if (reload) window.location.reload()
        return true
      }

      const [, , toLogin] = useLogin()
      const url = window.location.href
      const callbackUrl = urlAppend(url, { distri_biz_code: getBizCode(), biz_channel_code: curChannelBiz.get() })
      return new Promise((resolve) => {
        toLogin({
          callbackUrl,
          callback: (res) => {
            resolve(res === 2)
          }
        }, { useHistoryReplace: true, loginType: loginType.get() || '0' })
      })
    }
  }
})
