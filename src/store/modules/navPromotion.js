import { defineStore } from 'pinia'
import { getIconInfo } from '@api/interface/bannerIcon.js'

export const useNavPromotionStore = defineStore('navPromotion', {
  state: () => ({
    menuItem: null
  }),
  actions: {
    setMenuItem(payload) {
      this.menuItem = payload
    },
    async query({ bizCode }) {
      if (this.menuItem) return this.menuItem
      const [, json] = await getIconInfo({ bizCode, showPage: 3 })
      let menuItem = {}
      if (json && json.length > 0) {
        const menuList = json[0]
        menuItem = {
          title: '',
          path: menuList.url,
          img: menuList.imgUrl,
          activeImg: '',
          className: 'promotion-icon'
        }
      }
      this.setMenuItem(menuItem)
      return menuItem
    }
  }
})
