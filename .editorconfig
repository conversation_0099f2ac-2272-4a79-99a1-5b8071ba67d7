# EditorConfig 配置文件
# http://editorconfig.org

# 表明是最顶层的配置文件，发现设为true时，才会停止查找.editorconfig文件
root = true

# 对所有文件生效
[*]
charset = utf-8
indent_style = space
indent_size = 2
end_of_line = lf
trim_trailing_whitespace = true
insert_final_newline = true
max_line_length = 100

# 对JS、TS、Vue等前端文件的配置
[*.{js,jsx,ts,tsx,vue}]
indent_style = space
indent_size = 2
trim_trailing_whitespace = true
insert_final_newline = true

# 对CSS、SCSS、LESS等样式文件的配置
[*.{css,scss,less,styl}]
indent_style = space
indent_size = 2

# 对HTML和XML文件的配置
[*.{html,xml}]
indent_style = space
indent_size = 2

# 对JSON和YAML文件的配置
[*.{json,yml,yaml}]
indent_style = space
indent_size = 2

# 对Markdown文件的特殊配置
[*.md]
trim_trailing_whitespace = false
max_line_length = off

# 对package.json和package-lock.json的特殊配置
[package*.json]
indent_style = space
indent_size = 2

# 对Makefile的特殊配置
[Makefile]
indent_style = tab
