{"compilerOptions": {"target": "es5", "module": "esnext", "baseUrl": "./", "moduleResolution": "node", "paths": {"@/*": ["src/*"], "@src/*": ["src/*"], "@components/*": ["src/components/*"], "@api/*": ["src/api/*"], "@utils/*": ["src/utils/*"], "@assets/*": ["src/assets/*"], "@views/*": ["src/views/*"], "@store/*": ["src/store/*"], "@router/*": ["src/router/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.js", "src/**/*.vue"], "exclude": ["node_modules", "dist"]}