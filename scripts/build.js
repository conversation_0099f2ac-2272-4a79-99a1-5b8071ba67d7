import { execSync } from 'child_process';

const epayEnv = process.env.EPAYENV || 'xhm-k8s-prod';

// 环境到模式的映射
const modeMap = {
  'development': 'development',
  'xhm-k8s-sit': 'sit', 
  'xhm-k8s-uat': 'uat',
  'xhm-k8s-prod': 'production'
};

const mode = modeMap[epayEnv] || 'production';

console.log(`构建环境: ${epayEnv}, 使用模式: ${mode}`);

try {
  execSync(`cross-env EPAYENV=${epayEnv} vite build --mode ${mode}`, { stdio: 'inherit' });
} catch (error) {
  process.exit(1);
}