<!DOCTYPE html>
<html lang="">
<head>
  <meta charset="UTF-8">
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover">
  <link rel="preconnect" href="//img30.360buyimg.com">
  <link rel="preconnect" href="//img13.360buyimg.com">
  <link rel="dns-prefetch" href="//h5.cdn.unicompayment.com">
  <link rel="dns-prefetch" href="//media.cdn.unicompayment.com">
  <title></title>
</head>
<body>
<div id="app"></div>
<script type="module" src="/src/main.js"></script>
<script>
  if (/wopaywallet/i.test(window.navigator.userAgent)) document.title = '\u200E'
</script>
<script>
  // 检测网络状况并加载 BonreeSDK
  function loadBonreeSDK() {
    if (window.location.host !== 'epay.10010.com') return
    try {
      // 检查网络连接状态
      if (navigator.connection) {
        const connection = navigator.connection

        // 兼容性处理：某些浏览器可能返回不同的属性
        const effectiveType = (connection.effectiveType || '').toLowerCase()
        const type = (connection.type || '').toLowerCase()
        const downlink = connection.downlink || 0 // 下行速度(Mbps)

        // 允许 4G、5G、WiFi 及以太网环境 (不使用includes，改用indexOf)
        const isCellularValid = effectiveType === '4g' || effectiveType === '5g'
        const isWifiOrEthernet = type === 'wifi' || type === 'ethernet'

        // 兜底策略1：如果网络类型判断不准确，使用下行速度作为参考
        // 通常 2Mbps 以上可视为良好网络环境
        const hasGoodSpeed = downlink >= 1

        if (type === 'cellular' && !isCellularValid && !hasGoodSpeed) {
          console.log('[BonreeSDK] 低速蜂窝网络，跳过加载')
          return
        }

        if (!isWifiOrEthernet && type !== 'cellular' && !hasGoodSpeed) {
          console.log('[BonreeSDK] 网络类型不符合要求且速度较慢，跳过加载')
          return
        }
      } else {
        // 兜底策略2：如果无法获取网络信息，使用navigator.onLine判断
        if (!navigator.onLine) {
          console.log('[BonreeSDK] 设备离线，跳过加载')
          return
        }

        // 兜底策略3：如果无法获取详细网络信息，但设备在线，则使用性能API评估
        if (window.performance && window.performance.timing) {
          const navTiming = window.performance.timing
          const pageLoadTime = navTiming.loadEventEnd - navTiming.navigationStart

          // 如果页面加载时间过长，可能网络不佳
          if (pageLoadTime > 5000) { // 5秒以上视为加载缓慢
            console.log('[BonreeSDK] 页面加载时间过长，可能网络不佳，跳过加载')
            return
          }
        }
      }
    } catch (error) {
      // 兜底处理：如果网络检测出错，记录错误并继续加载SDK
      console.warn('[BonreeSDK] 网络检测出错，将继续加载SDK:', error)
      // 这里不返回，继续执行下面的加载逻辑
    }

    // 网络状况良好或检测出错但仍继续，加载SDK
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = 'https://media.cdn.unicompayment.com/ito-ffms/res/bonree/2.4.0/BonreeSDK_JS.min.js'
    script.id = 'BonreeAgent'
    const data = {
      'reqHeaderTraceKey': ['tracestate', 'traceparent'],
      'uploadAddrHttps': 'https://br.unicompayment.com/RUM/upload',
      'mc': [{ 'n': 'network', 'cs': { 'fc': 0 } }],
      'appId': 'ac4c2c13636d452fa5bc49cb17b1b216',
      'uploadAddrHttp': 'http://br.unicompayment.com/RUM/upload',
      'respHeaderTraceKey': ['traceresponse', 'x-br-response'],
      'brss': false
    }
    const encodedData = encodeURIComponent(JSON.stringify(data))
    script.setAttribute('data', encodedData)
    document.head.appendChild(script)
    console.log('[BonreeSDK] 已加载')
  }


  // 使用 requestIdleCallback 在浏览器空闲时加载，避免影响页面渲染
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => loadBonreeSDK(), { timeout: 5000 })
  } else {
    // 降级处理：延迟加载
    setTimeout(loadBonreeSDK, 3000)
  }
</script>
<% if (process.env.EPAYENV==='xhm-k8s-uat' ||process.env.EPAYENV==='xhm-k8s-sit' ){ %>
<script src="<%= process.env.BASE_URL %>vconsole.min.js"></script>
<script>
  new VConsole()
</script>
<% } %>
</body>
</html>
